#!/usr/bin/env python3
"""
高级BERT融合搜索引擎
使用BERT、RoBERTa、Sentence-BERT等多个预训练模型进行特征融合检索
专门优化中文支持能力
"""

import os
import time
import numpy as np
import pandas as pd
import torch
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# 导入transformers模型
from transformers import (
    AutoTokenizer, AutoModel,
    BertTokenizer, BertModel,
    RobertaTokenizer, RobertaModel,
    pipeline
)
from sentence_transformers import SentenceTransformer

# 中文NLP工具
import jieba
import jieba.posseg as pseg
import re
from collections import defaultdict

# 科学计算
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer

@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    model_path: str
    weight: float
    enabled: bool = True
    model_type: str = "bert"  # bert, roberta, sentence_bert

@dataclass
class SearchResult:
    """搜索结果"""
    id: str
    description: str
    dataset_name: str
    category_name: str
    subcategory_name: str
    region: str
    universe: str
    delay: str
    
    # 分数信息
    overall_score: float
    model_scores: Dict[str, float]
    column_scores: Dict[str, float]
    
    # 匹配信息
    matched_columns: List[str]
    matched_keywords: List[str]
    search_mode: str

class ChineseFinancialNLP:
    """中文金融NLP处理器"""
    
    def __init__(self):
        self.financial_terms = self._load_financial_terms()
        self.stop_words = self._load_stop_words()
        self.synonym_dict = self._load_synonyms()
        
        # 初始化jieba
        self._init_jieba()
        
        # 预编译正则表达式
        self.patterns = {
            'percentage': re.compile(r'\d+\.?\d*%'),
            'currency': re.compile(r'[¥$€£]\d+\.?\d*[万亿千百十]?'),
            'time_period': re.compile(r'\d+[年月日季度]|[上下]半年|年初|年末'),
            'financial_metric': re.compile(r'(每股收益|净资产收益率|市盈率|ROE|EPS|EBITDA)'),
            'analyst_terms': re.compile(r'(分析师|研究员|评级|目标价|买入|卖出|持有)')
        }
    
    def _load_financial_terms(self) -> set:
        """加载金融术语词典"""
        return {
            # 中文金融指标
            '每股收益', '净资产收益率', '总资产收益率', '毛利率', '净利率',
            '营业利润率', '资产负债率', '流动比率', '速动比率', '市盈率',
            '市净率', '市销率', '营业收入', '净利润', '毛利润', '现金流',
            '自由现金流', '经营现金流', '投资现金流', '筹资现金流',
            
            # 分析师相关
            '分析师', '研究员', '分析员', '研报', '评级', '目标价', '盈利预测',
            '一致预期', '买入', '卖出', '持有', '增持', '减持', '中性', '推荐',
            
            # 英文术语
            'EPS', 'ROE', 'ROA', 'PE', 'PB', 'PS', 'EBITDA', 'EBIT',
            'earnings', 'revenue', 'profit', 'cash flow', 'analyst',
            'estimate', 'forecast', 'rating', 'target price', 'buy', 'sell', 'hold'
        }
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        return {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都',
            '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着',
            '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是',
            '为了', '还有', '可以', '这个', '那个', '什么', '怎么', '为什么',
            '因为', '所以', '但是', '然后', '如果', '虽然', '数据', '指标'
        }
    
    def _load_synonyms(self) -> Dict[str, List[str]]:
        """加载同义词词典"""
        return {
            '每股收益': ['EPS', '每股盈利', '每股净收益', 'earnings per share'],
            '净资产收益率': ['ROE', '股东权益回报率', 'return on equity'],
            '总资产收益率': ['ROA', '资产回报率', 'return on assets'],
            '市盈率': ['PE', 'P/E', '价格收益比', 'price earnings ratio'],
            '市净率': ['PB', 'P/B', '价格净值比', 'price book ratio'],
            '营业收入': ['营收', '销售收入', '主营业务收入', 'revenue', 'sales'],
            '净利润': ['净收益', '税后利润', 'net profit', 'net income'],
            '分析师': ['研究员', '分析员', 'analyst', 'researcher'],
            '预测': ['预期', '预估', '估算', 'forecast', 'estimate', 'projection'],
            '增长': ['增长率', '增速', '涨幅', 'growth', 'increase'],
            '现金流': ['现金流量', '资金流', 'cash flow'],
            'EBITDA': ['息税折旧摊销前利润', 'earnings before interest tax depreciation amortization']
        }
    
    def _init_jieba(self):
        """初始化jieba分词器"""
        # 添加金融术语到jieba词典
        for term in self.financial_terms:
            jieba.add_word(term, freq=2000, tag='financial')
        
        # 添加同义词
        for key, synonyms in self.synonym_dict.items():
            jieba.add_word(key, freq=1500, tag='financial')
            for synonym in synonyms:
                jieba.add_word(synonym, freq=1200, tag='financial')
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取命名实体"""
        entities = defaultdict(list)
        
        if not text:
            return dict(entities)
        
        # 使用正则表达式提取实体
        for entity_type, pattern in self.patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[entity_type].extend(matches)
        
        return dict(entities)
    
    def segment_and_pos_tag(self, text: str) -> List[Tuple[str, str]]:
        """中文分词和词性标注"""
        if not text:
            return []
        
        words_with_pos = pseg.lcut(text)
        filtered_results = []
        
        for word, pos in words_with_pos:
            word = word.strip()
            if (len(word) >= 1 and 
                not word.isspace() and
                word not in self.stop_words):
                filtered_results.append((word, pos))
        
        return filtered_results
    
    def extract_keywords(self, text: str, top_k: int = 15) -> List[Tuple[str, float]]:
        """提取关键词和权重"""
        words_with_pos = self.segment_and_pos_tag(text)
        word_weights = defaultdict(float)
        
        for word, pos in words_with_pos:
            weight = 1.0
            
            # 金融术语权重
            if word in self.financial_terms:
                weight *= 5.0
            
            # 词性权重
            if pos.startswith('n'):  # 名词
                weight *= 3.0
            elif pos.startswith('v'):  # 动词
                weight *= 2.0
            elif pos.startswith('a'):  # 形容词
                weight *= 1.5
            elif pos in ['m', 'q']:  # 数词、量词
                weight *= 2.5
            
            # 词长权重
            if len(word) >= 4:
                weight *= 2.5
            elif len(word) >= 3:
                weight *= 2.0
            elif len(word) >= 2:
                weight *= 1.5
            
            # 中英文混合权重
            if re.search(r'[\u4e00-\u9fff]', word) and re.search(r'[a-zA-Z]', word):
                weight *= 2.0
            
            word_weights[word] += weight
        
        # 排序并返回top_k
        sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
        return sorted_words[:top_k]
    
    def expand_query(self, query: str) -> Dict[str, List[str]]:
        """智能查询扩展"""
        expanded = {
            'original': [query],
            'synonyms': [],
            'related_terms': [],
            'entities': []
        }
        
        # 分词
        words = [word for word, pos in self.segment_and_pos_tag(query)]
        
        # 同义词扩展
        for word in words:
            if word in self.synonym_dict:
                expanded['synonyms'].extend(self.synonym_dict[word])
            
            # 反向查找同义词
            for key, synonyms in self.synonym_dict.items():
                if word in synonyms and key not in expanded['synonyms']:
                    expanded['synonyms'].append(key)
        
        # 实体扩展
        entities = self.extract_entities(query)
        for entity_type, entity_list in entities.items():
            expanded['entities'].extend(entity_list)
        
        # 去重
        for key in expanded:
            expanded[key] = list(set(expanded[key]))
        
        return expanded

class BERTFusionEngine:
    """BERT融合搜索引擎"""
    
    def __init__(self):
        """初始化BERT融合引擎"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 Using device: {self.device}")
        
        # 模型配置
        self.model_configs = {
            'chinese_bert': ModelConfig(
                name='chinese_bert',
                model_path='bert-base-chinese',
                weight=0.3,
                model_type='bert'
            ),
            'multilingual_bert': ModelConfig(
                name='multilingual_bert', 
                model_path='bert-base-multilingual-cased',
                weight=0.25,
                model_type='bert'
            ),
            'sentence_bert': ModelConfig(
                name='sentence_bert',
                model_path='paraphrase-multilingual-MiniLM-L12-v2',
                weight=0.45,
                model_type='sentence_bert'
            )
        }
        
        # 存储加载的模型
        self.models = {}
        self.tokenizers = {}
        
        # 中文NLP处理器
        self.chinese_nlp = ChineseFinancialNLP()
        
        # 数据存储
        self.df = None
        self.column_features = {}
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
    
    def load_models(self):
        """加载所有预训练模型"""
        print("📥 Loading pre-trained models...")
        
        for config_name, config in self.model_configs.items():
            if not config.enabled:
                continue
            
            print(f"   Loading {config.name}: {config.model_path}")
            
            try:
                if config.model_type == 'sentence_bert':
                    # Sentence-BERT模型
                    model = SentenceTransformer(config.model_path)
                    self.models[config_name] = model
                    print(f"   ✅ {config.name} loaded successfully")
                    
                else:
                    # BERT/RoBERTa模型
                    tokenizer = AutoTokenizer.from_pretrained(config.model_path)
                    model = AutoModel.from_pretrained(config.model_path)
                    
                    # 移动到设备
                    model = model.to(self.device)
                    model.eval()
                    
                    self.tokenizers[config_name] = tokenizer
                    self.models[config_name] = model
                    print(f"   ✅ {config.name} loaded successfully")
                    
            except Exception as e:
                print(f"   ❌ Failed to load {config.name}: {e}")
                config.enabled = False
        
        print(f"✅ Successfully loaded {len(self.models)} models")
    
    def encode_text_with_bert(self, text: str, model_name: str) -> np.ndarray:
        """使用BERT模型编码文本"""
        if model_name not in self.models or model_name not in self.tokenizers:
            return np.array([])
        
        try:
            tokenizer = self.tokenizers[model_name]
            model = self.models[model_name]
            
            # 分词和编码
            inputs = tokenizer(
                text, 
                return_tensors='pt',
                truncation=True,
                max_length=512,
                padding=True
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model(**inputs)
                # 使用[CLS]token的表示或平均池化
                if hasattr(outputs, 'pooler_output') and outputs.pooler_output is not None:
                    embeddings = outputs.pooler_output
                else:
                    # 平均池化
                    embeddings = outputs.last_hidden_state.mean(dim=1)
                
                return embeddings.cpu().numpy()[0]
                
        except Exception as e:
            print(f"⚠️ BERT encoding failed for {model_name}: {e}")
            return np.array([])
    
    def encode_text_with_sentence_bert(self, text: str, model_name: str) -> np.ndarray:
        """使用Sentence-BERT编码文本"""
        if model_name not in self.models:
            return np.array([])
        
        try:
            model = self.models[model_name]
            embeddings = model.encode([text])
            return embeddings[0]
            
        except Exception as e:
            print(f"⚠️ Sentence-BERT encoding failed for {model_name}: {e}")
            return np.array([])
    
    def encode_text_multi_model(self, text: str) -> Dict[str, np.ndarray]:
        """使用多个模型编码文本"""
        encodings = {}
        
        for config_name, config in self.model_configs.items():
            if not config.enabled or config_name not in self.models:
                continue
            
            if config.model_type == 'sentence_bert':
                encoding = self.encode_text_with_sentence_bert(text, config_name)
            else:
                encoding = self.encode_text_with_bert(text, config_name)
            
            if encoding.size > 0:
                encodings[config_name] = encoding
        
        return encodings
    
    def build_fusion_index(self, df: pd.DataFrame):
        """构建融合索引"""
        print(f"🔨 Building BERT fusion index for {len(df)} records...")
        
        self.df = df
        start_time = time.time()
        
        # 加载模型
        self.load_models()
        
        target_columns = list(self.column_weights.keys())
        
        # 处理每一行数据
        for idx, row in df.iterrows():
            if idx % 100 == 0:
                print(f"   Processing row {idx}/{len(df)}")
            
            # 提取列特征
            column_features = {}
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # NLP分析
                    keywords = self.chinese_nlp.extract_keywords(text_content, top_k=10)
                    entities = self.chinese_nlp.extract_entities(text_content)
                    
                    # 多模型编码
                    embeddings = self.encode_text_multi_model(text_content)
                    
                    column_features[col_name] = {
                        'text': text_content,
                        'keywords': keywords,
                        'entities': entities,
                        'embeddings': embeddings
                    }
            
            # 存储特征
            self.column_features[idx] = {
                'columns': column_features,
                'row_data': row.to_dict()
            }
        
        build_time = time.time() - start_time
        print(f"✅ BERT fusion index built in {build_time:.2f}s")
    
    def calculate_similarity_scores(self, query_embeddings: Dict[str, np.ndarray], 
                                   doc_embeddings: Dict[str, np.ndarray]) -> Dict[str, float]:
        """计算相似度分数"""
        similarity_scores = {}
        
        for model_name in query_embeddings:
            if model_name in doc_embeddings:
                try:
                    query_vec = query_embeddings[model_name]
                    doc_vec = doc_embeddings[model_name]
                    
                    # 计算余弦相似度
                    similarity = cosine_similarity(
                        query_vec.reshape(1, -1), 
                        doc_vec.reshape(1, -1)
                    )[0][0]
                    
                    similarity_scores[model_name] = float(similarity)
                    
                except Exception as e:
                    print(f"⚠️ Similarity calculation failed for {model_name}: {e}")
                    similarity_scores[model_name] = 0.0
        
        return similarity_scores
    
    def bert_fusion_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """执行BERT融合搜索"""
        if not self.column_features:
            raise ValueError("Fusion index not built. Please build index first.")
        
        print(f"🔍 BERT fusion search for: '{query}'")
        start_time = time.time()
        
        # 查询预处理和扩展
        expanded_query = self.chinese_nlp.expand_query(query)
        query_keywords = self.chinese_nlp.extract_keywords(query, top_k=10)
        
        # 编码查询
        query_embeddings = self.encode_text_multi_model(query)
        
        # 搜索结果
        search_results = []
        
        # 遍历所有文档
        for doc_idx, doc_data in self.column_features.items():
            column_features = doc_data['columns']
            row_data = doc_data['row_data']
            
            # 计算每列的分数
            column_scores = {}
            model_scores = defaultdict(float)
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []
            
            for col_name, col_feature in column_features.items():
                # 关键词匹配分数
                keyword_score = self._calculate_keyword_score(
                    query_keywords, col_feature['keywords']
                )
                
                # 多模型语义相似度分数
                semantic_scores = self.calculate_similarity_scores(
                    query_embeddings, col_feature['embeddings']
                )
                
                # 加权平均语义分数
                weighted_semantic_score = 0.0
                for model_name, similarity in semantic_scores.items():
                    weight = self.model_configs[model_name].weight
                    weighted_semantic_score += similarity * weight
                    model_scores[model_name] += similarity * self.column_weights[col_name]
                
                # 综合列分数
                col_score = keyword_score * 0.3 + weighted_semantic_score * 0.7
                column_scores[col_name] = col_score
                
                # 加权到总分
                overall_score += col_score * self.column_weights[col_name]
                
                # 记录匹配信息
                if col_score > 0.1:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords']])
            
            # 创建搜索结果
            if overall_score > 0.01:
                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),
                    
                    overall_score=overall_score,
                    model_scores=dict(model_scores),
                    column_scores=column_scores,
                    
                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='bert_fusion'
                )
                
                search_results.append(result)
        
        # 排序并返回top_k结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)
        
        search_time = time.time() - start_time
        print(f"✅ BERT fusion search completed in {search_time:.3f}s, found {len(search_results)} results")
        
        return search_results[:top_k]
    
    def _calculate_keyword_score(self, query_keywords: List[Tuple[str, float]], 
                                doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词匹配分数"""
        if not query_keywords or not doc_keywords:
            return 0.0
        
        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}
        
        score = 0.0
        total_query_weight = sum(query_dict.values())
        
        for word, weight in query_dict.items():
            if word in doc_dict:
                # 完全匹配
                score += min(weight, doc_dict[word])
            else:
                # 部分匹配
                for doc_word in doc_dict:
                    if word in doc_word or doc_word in word:
                        score += weight * 0.5
                        break
        
        return score / (total_query_weight + 1e-8)

if __name__ == "__main__":
    # 示例用法
    engine = BERTFusionEngine()
    
    # 加载测试数据
    print("Loading test data...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv").head(200)  # 测试200条数据
    
    # 构建融合索引
    engine.build_fusion_index(df)
    
    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share", 
        "分析师预测",
        "EBITDA相关指标",
        "净资产收益率 ROE"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = engine.bert_fusion_search(query, top_k=5)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.id} (Score: {result.overall_score:.4f})")
            print(f"   Description: {result.description[:80]}...")
            print(f"   Model scores: {result.model_scores}")
            print(f"   Matched columns: {result.matched_columns}")
            print()
