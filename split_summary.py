#!/usr/bin/env python3
"""
Generate a summary report of the split files
"""

import os
import pandas as pd
from collections import defaultdict

def generate_summary_report(split_dir='split_files'):
    """Generate a summary report of all split files"""
    
    if not os.path.exists(split_dir):
        print(f"Directory {split_dir} not found!")
        return
    
    print("=" * 80)
    print("DATA.CSV SPLIT SUMMARY REPORT")
    print("=" * 80)
    
    # Get all CSV files
    csv_files = [f for f in os.listdir(split_dir) if f.endswith('.csv')]
    csv_files.sort()
    
    total_records = 0
    region_stats = defaultdict(int)
    delay_stats = defaultdict(int)
    universe_stats = defaultdict(int)
    combination_stats = []
    
    print(f"\nTotal files created: {len(csv_files)}")
    print("\nDetailed breakdown:")
    print("-" * 80)
    print(f"{'Filename':<35} {'Records':<10} {'Size':<10} {'Region':<8} {'Delay':<6} {'Universe'}")
    print("-" * 80)
    
    for filename in csv_files:
        filepath = os.path.join(split_dir, filename)
        
        # Get file size
        file_size = os.path.getsize(filepath)
        if file_size > 1024 * 1024:
            size_str = f"{file_size / (1024 * 1024):.1f}MB"
        elif file_size > 1024:
            size_str = f"{file_size / 1024:.1f}KB"
        else:
            size_str = f"{file_size}B"
        
        # Count records (excluding header)
        try:
            with open(filepath, 'r') as f:
                record_count = sum(1 for line in f) - 1
        except:
            record_count = 0
        
        # Parse filename to extract region, delay, universe
        parts = filename.replace('.csv', '').split('_')
        if len(parts) >= 3:
            region = parts[0]
            delay = parts[1]
            universe = '_'.join(parts[2:])
        else:
            region = delay = universe = "Unknown"
        
        print(f"{filename:<35} {record_count:<10} {size_str:<10} {region:<8} {delay:<6} {universe}")
        
        total_records += record_count
        region_stats[region] += record_count
        delay_stats[delay] += record_count
        universe_stats[universe] += record_count
        combination_stats.append((region, delay, universe, record_count))
    
    print("-" * 80)
    print(f"{'TOTAL':<35} {total_records:<10}")
    
    # Summary by region
    print(f"\n\nSUMMARY BY REGION:")
    print("-" * 40)
    for region, count in sorted(region_stats.items()):
        percentage = (count / total_records) * 100
        print(f"{region:<15} {count:>10} ({percentage:>5.1f}%)")
    
    # Summary by delay
    print(f"\n\nSUMMARY BY DELAY:")
    print("-" * 40)
    for delay, count in sorted(delay_stats.items()):
        percentage = (count / total_records) * 100
        print(f"{delay:<15} {count:>10} ({percentage:>5.1f}%)")
    
    # Summary by universe (top 10)
    print(f"\n\nSUMMARY BY UNIVERSE (Top 10):")
    print("-" * 40)
    sorted_universe = sorted(universe_stats.items(), key=lambda x: x[1], reverse=True)
    for universe, count in sorted_universe[:10]:
        percentage = (count / total_records) * 100
        print(f"{universe:<15} {count:>10} ({percentage:>5.1f}%)")
    
    # Top combinations
    print(f"\n\nTOP 10 COMBINATIONS BY RECORD COUNT:")
    print("-" * 60)
    print(f"{'Region':<8} {'Delay':<6} {'Universe':<20} {'Records':<10}")
    print("-" * 60)
    combination_stats.sort(key=lambda x: x[3], reverse=True)
    for region, delay, universe, count in combination_stats[:10]:
        print(f"{region:<8} {delay:<6} {universe:<20} {count:<10}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    generate_summary_report()
