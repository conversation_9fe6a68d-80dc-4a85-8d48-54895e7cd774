{"base_config": {"min_score_threshold": 0.01, "semantic_threshold": 0.3, "keyword_threshold": 0.2, "min_results": 1, "max_results": 100, "target_results": 10, "quality_threshold": 0.5, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "query_type_configs": {"exact_match": {"min_score_threshold": 0.1, "semantic_threshold": 0.6, "keyword_threshold": 0.8, "min_results": 1, "max_results": 100, "target_results": 5, "quality_threshold": 0.8, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "semantic_search": {"min_score_threshold": 0.05, "semantic_threshold": 0.4, "keyword_threshold": 0.3, "min_results": 1, "max_results": 100, "target_results": 10, "quality_threshold": 0.6, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "keyword_search": {"min_score_threshold": 0.02, "semantic_threshold": 0.2, "keyword_threshold": 0.6, "min_results": 1, "max_results": 100, "target_results": 15, "quality_threshold": 0.5, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "mixed_language": {"min_score_threshold": 0.01, "semantic_threshold": 0.3, "keyword_threshold": 0.4, "min_results": 1, "max_results": 100, "target_results": 12, "quality_threshold": 0.4, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "financial_term": {"min_score_threshold": 0.08, "semantic_threshold": 0.5, "keyword_threshold": 0.7, "min_results": 1, "max_results": 100, "target_results": 8, "quality_threshold": 0.7, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "analyst_query": {"min_score_threshold": 0.03, "semantic_threshold": 0.4, "keyword_threshold": 0.5, "min_results": 1, "max_results": 100, "target_results": 10, "quality_threshold": 0.6, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}, "complex_query": {"min_score_threshold": 0.01, "semantic_threshold": 0.25, "keyword_threshold": 0.3, "min_results": 1, "max_results": 100, "target_results": 20, "quality_threshold": 0.4, "diversity_threshold": 0.8, "adjustment_rate": 0.1, "learning_rate": 0.05, "decay_factor": 0.95}}, "threshold_adjustments": {}, "success_rates": {"keyword_search": [0.6956978103101253, 0.5749115619421005], "analyst_query": [0.9699999999999999, 0.8920106285862636, 0.768900544667244, 0.9949999999999999]}}