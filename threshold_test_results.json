[{"scenario": "EPS相关查询测试", "query": "每股收益", "context": "interactive", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.23474502563476562, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "EPS相关查询测试", "query": "EPS growth", "context": "interactive", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.1685199737548828, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "EPS相关查询测试", "query": "earnings per share", "context": "interactive", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.16980910301208496, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "EPS相关查询测试", "query": "每股盈利增长", "context": "interactive", "results_count": 10, "avg_score": 0.18837226288765668, "max_score": 0.20297092504799366, "search_time": 0.25315189361572266, "user_satisfaction": 0.45, "click_through_rate": 0.4070738986541004}, {"scenario": "分析师相关查询测试", "query": "分析师预测", "context": "api", "results_count": 10, "avg_score": 0.5176380538936187, "max_score": 0.5208941909517576, "search_time": 0.15242981910705566, "user_satisfaction": 0.7, "click_through_rate": 0.6242805744209114}, {"scenario": "分析师相关查询测试", "query": "analyst forecast", "context": "api", "results_count": 10, "avg_score": 0.4025132857328294, "max_score": 0.43200544567740573, "search_time": 0.16964316368103027, "user_satisfaction": 0.7, "click_through_rate": 0.7464242398047983}, {"scenario": "分析师相关查询测试", "query": "研究员评级", "context": "api", "results_count": 10, "avg_score": 0.26737568083405494, "max_score": 0.27353957265615464, "search_time": 0.15656805038452148, "user_satisfaction": 0.55, "click_through_rate": 0.5611930806956257}, {"scenario": "分析师相关查询测试", "query": "analyst estimates", "context": "api", "results_count": 10, "avg_score": 0.6330061648885931, "max_score": 0.66522568578434, "search_time": 0.14895081520080566, "user_satisfaction": 0.9500000000000001, "click_through_rate": 0.8135365863077618}, {"scenario": "EBITDA相关查询测试", "query": "EBITDA", "context": "real_time", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.1512770652770996, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "EBITDA相关查询测试", "query": "EBITDA相关指标", "context": "real_time", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.15423893928527832, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "EBITDA相关查询测试", "query": "息税折旧摊销前利润", "context": "real_time", "results_count": 5, "avg_score": 0.2123894524276256, "max_score": 0.23621531859040254, "search_time": 0.15702104568481445, "user_satisfaction": 0.55, "click_through_rate": 0.6169678453531331}, {"scenario": "EBITDA相关查询测试", "query": "EBITDA growth", "context": "real_time", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.17681217193603516, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "精确ID查询测试", "query": "act_12m_eps_value", "context": "batch", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.16219186782836914, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "精确ID查询测试", "query": "pv87_ebitda_actual", "context": "batch", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.15008020401000977, "user_satisfaction": 0.1, "click_through_rate": 0.0}, {"scenario": "精确ID查询测试", "query": "mdl23_bk_return_on_equity", "context": "batch", "results_count": 0, "avg_score": 0.0, "max_score": 0.0, "search_time": 0.15850281715393066, "user_satisfaction": 0.1, "click_through_rate": 0.0}]