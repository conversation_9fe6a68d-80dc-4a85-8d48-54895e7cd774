#!/usr/bin/env python3
"""
动态阈值调整测试
演示智能阈值调整如何提升搜索质量和用户体验
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any
import json

# 导入我们的搜索引擎和阈值管理器
from single_bert_fusion_engine import SingleBERTFusionEngine
from dynamic_threshold_manager import SearchContext, QueryType

def simulate_user_feedback(results: List[Any], query: str) -> Dict[str, float]:
    """模拟用户反馈"""
    if not results:
        return {'satisfaction': 0.1, 'click_through_rate': 0.0}
    
    # 基于结果质量模拟用户满意度
    avg_score = np.mean([r.overall_score for r in results])
    result_count = len(results)
    
    # 满意度计算
    satisfaction = 0.0
    
    # 结果数量影响 (30%)
    if result_count >= 3:
        satisfaction += 0.3
    elif result_count >= 1:
        satisfaction += 0.15
    
    # 结果质量影响 (50%)
    if avg_score > 0.6:
        satisfaction += 0.5
    elif avg_score > 0.4:
        satisfaction += 0.35
    elif avg_score > 0.2:
        satisfaction += 0.2
    else:
        satisfaction += 0.1
    
    # 查询匹配度影响 (20%)
    query_lower = query.lower()
    if any('eps' in r.id.lower() or 'earnings' in r.description.lower() 
           for r in results if 'eps' in query_lower or 'earnings' in query_lower):
        satisfaction += 0.2
    elif any(word in r.description.lower() for r in results 
             for word in query_lower.split()):
        satisfaction += 0.15
    else:
        satisfaction += 0.05
    
    # 点击率模拟
    click_through_rate = min(1.0, satisfaction * 0.8 + np.random.uniform(0, 0.2))
    
    return {
        'satisfaction': min(1.0, satisfaction),
        'click_through_rate': click_through_rate
    }

def test_dynamic_threshold_adaptation():
    """测试动态阈值适应性"""
    print("🧪 Dynamic Threshold Adaptation Test")
    print("=" * 60)
    
    # 初始化搜索引擎
    engine = SingleBERTFusionEngine()
    
    # 加载测试数据
    print("📊 Loading test data...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv").head(200)  # 使用200条数据进行快速测试
    
    # 构建索引
    engine.build_fusion_index(df)
    
    # 测试查询集合
    test_scenarios = [
        {
            'queries': [
                "每股收益",
                "EPS growth",
                "earnings per share",
                "每股盈利增长"
            ],
            'context': SearchContext.INTERACTIVE,
            'description': "EPS相关查询测试"
        },
        {
            'queries': [
                "分析师预测",
                "analyst forecast",
                "研究员评级",
                "analyst estimates"
            ],
            'context': SearchContext.API_CALL,
            'description': "分析师相关查询测试"
        },
        {
            'queries': [
                "EBITDA",
                "EBITDA相关指标",
                "息税折旧摊销前利润",
                "EBITDA growth"
            ],
            'context': SearchContext.REAL_TIME,
            'description': "EBITDA相关查询测试"
        },
        {
            'queries': [
                "act_12m_eps_value",
                "pv87_ebitda_actual",
                "mdl23_bk_return_on_equity"
            ],
            'context': SearchContext.BATCH_PROCESSING,
            'description': "精确ID查询测试"
        }
    ]
    
    print("\n🔄 开始动态阈值适应性测试...")
    
    all_results = []
    
    for scenario_idx, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 场景 {scenario_idx}: {scenario['description']}")
        print(f"   上下文: {scenario['context'].value}")
        
        scenario_results = []
        
        for query_idx, query in enumerate(scenario['queries'], 1):
            print(f"\n   🔍 查询 {query_idx}: '{query}'")
            
            # 执行搜索
            start_time = time.time()
            results = engine.bert_fusion_search(
                query, 
                top_k=10, 
                search_context=scenario['context']
            )
            search_time = time.time() - start_time
            
            # 模拟用户反馈
            feedback = simulate_user_feedback(results, query)
            
            # 更新用户反馈
            engine.update_user_feedback(
                query, 
                feedback['satisfaction'], 
                feedback['click_through_rate']
            )
            
            # 记录结果
            result_info = {
                'scenario': scenario['description'],
                'query': query,
                'context': scenario['context'].value,
                'results_count': len(results),
                'avg_score': np.mean([r.overall_score for r in results]) if results else 0.0,
                'max_score': max([r.overall_score for r in results]) if results else 0.0,
                'search_time': search_time,
                'user_satisfaction': feedback['satisfaction'],
                'click_through_rate': feedback['click_through_rate']
            }
            
            scenario_results.append(result_info)
            all_results.append(result_info)
            
            # 显示结果摘要
            print(f"      📊 结果数量: {len(results)}")
            print(f"      📈 平均分数: {result_info['avg_score']:.3f}")
            print(f"      ⏱️  搜索时间: {search_time:.3f}s")
            print(f"      😊 用户满意度: {feedback['satisfaction']:.3f}")
            print(f"      👆 点击率: {feedback['click_through_rate']:.3f}")
            
            # 显示前3个结果
            for i, result in enumerate(results[:3]):
                print(f"         {i+1}. {result.id} (分数: {result.overall_score:.3f})")
        
        # 场景总结
        avg_satisfaction = np.mean([r['user_satisfaction'] for r in scenario_results])
        avg_results = np.mean([r['results_count'] for r in scenario_results])
        avg_time = np.mean([r['search_time'] for r in scenario_results])
        
        print(f"\n   📊 场景总结:")
        print(f"      平均满意度: {avg_satisfaction:.3f}")
        print(f"      平均结果数: {avg_results:.1f}")
        print(f"      平均搜索时间: {avg_time:.3f}s")
    
    return all_results, engine

def analyze_threshold_performance(results: List[Dict], engine: SingleBERTFusionEngine):
    """分析阈值性能"""
    print(f"\n📈 Dynamic Threshold Performance Analysis")
    print("=" * 60)
    
    # 获取优化报告
    optimization_report = engine.get_threshold_optimization_report()
    
    # 整体性能分析
    print(f"📊 Overall Performance:")
    overall_perf = optimization_report.get('overall_performance', {})
    for key, value in overall_perf.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.3f}")
        else:
            print(f"   {key}: {value}")
    
    # 查询类型分析
    print(f"\n🎯 Query Type Analysis:")
    query_analysis = optimization_report.get('query_type_analysis', {})
    for qtype, analysis in query_analysis.items():
        print(f"   {qtype}:")
        print(f"      搜索次数: {analysis.get('search_count', 0)}")
        print(f"      成功率: {analysis.get('avg_success_rate', 0):.3f}")
        
        thresholds = analysis.get('current_thresholds', {})
        print(f"      当前阈值 - 最小: {thresholds.get('min_score_threshold', 0):.3f}, "
              f"语义: {thresholds.get('semantic_threshold', 0):.3f}, "
              f"关键词: {thresholds.get('keyword_threshold', 0):.3f}")
    
    # 优化建议
    print(f"\n💡 Optimization Suggestions:")
    suggestions = optimization_report.get('optimization_suggestions', [])
    if suggestions:
        for suggestion in suggestions:
            print(f"   🔧 {suggestion['query_type']}: {suggestion['suggestion']}")
            print(f"      优先级: {suggestion['priority']}")
    else:
        print("   ✅ No optimization suggestions - system is performing well!")
    
    # 用户满意度分析
    print(f"\n😊 User Satisfaction Analysis:")
    satisfaction_by_context = {}
    for result in results:
        context = result['context']
        if context not in satisfaction_by_context:
            satisfaction_by_context[context] = []
        satisfaction_by_context[context].append(result['user_satisfaction'])
    
    for context, satisfactions in satisfaction_by_context.items():
        avg_satisfaction = np.mean(satisfactions)
        print(f"   {context}: {avg_satisfaction:.3f} (基于 {len(satisfactions)} 次搜索)")
    
    # 性能改进建议
    print(f"\n🚀 Performance Improvement Recommendations:")
    
    overall_satisfaction = np.mean([r['user_satisfaction'] for r in results])
    if overall_satisfaction < 0.6:
        print("   📉 整体满意度较低，建议:")
        print("      - 降低最小分数阈值以提高召回率")
        print("      - 增加同义词词典覆盖范围")
        print("      - 优化中文分词和实体识别")
    elif overall_satisfaction > 0.9:
        print("   📈 整体满意度很高，可以考虑:")
        print("      - 适当提高阈值以提升精确度")
        print("      - 减少返回结果数量以提高响应速度")
    else:
        print("   ✅ 系统性能良好，继续监控和微调")
    
    # 保存配置
    engine.save_threshold_config()

def main():
    """主测试函数"""
    print("🎛️ Dynamic Threshold Management System Test")
    print("集成智能阈值调整的BERT融合搜索引擎")
    print("=" * 80)
    
    # 执行动态阈值适应性测试
    results, engine = test_dynamic_threshold_adaptation()
    
    # 分析阈值性能
    analyze_threshold_performance(results, engine)
    
    # 保存测试结果
    with open('threshold_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试结果已保存到 threshold_test_results.json")
    print(f"🎉 动态阈值管理系统测试完成!")
    
    # 显示系统优势
    print(f"\n🌟 Dynamic Threshold System Advantages:")
    print(f"   1. 🎯 智能查询类型识别 - 自动分类7种查询类型")
    print(f"   2. 🔄 自适应阈值调整 - 根据历史性能动态优化")
    print(f"   3. 📊 多维度性能监控 - 实时跟踪搜索质量")
    print(f"   4. 😊 用户反馈学习 - 基于满意度持续改进")
    print(f"   5. 🎛️ 灵活配置管理 - 支持不同场景定制化")
    print(f"   6. 📈 性能分析报告 - 提供详细优化建议")

if __name__ == "__main__":
    main()
