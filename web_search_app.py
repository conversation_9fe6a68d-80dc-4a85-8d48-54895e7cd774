#!/usr/bin/env python3
"""
BERT融合搜索引擎 Web应用
集成动态阈值调整的交互式搜索界面
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
import pandas as pd
import numpy as np
import time
import uuid
import json
from typing import Dict, List, Any
import os

# 导入优化的搜索引擎
from cached_search_engine import CachedSearchEngine
from optimized_model_manager import CacheConfig
from dynamic_threshold_manager import SearchContext
from interactive_search_engine import InteractiveSearchEngine

app = Flask(__name__)
app.secret_key = 'bert_fusion_search_engine_2024'
CORS(app)

# 全局搜索引擎实例
cached_engine = None
interactive_engine = None
data_loaded = False
initialization_status = {"status": "not_started", "progress": 0, "message": ""}

def initialize_search_engines():
    """初始化搜索引擎"""
    global cached_engine, interactive_engine, data_loaded, initialization_status

    try:
        initialization_status.update({
            "status": "initializing",
            "progress": 10,
            "message": "Initializing cached search engine..."
        })

        print("🚀 Initializing Cached BERT Fusion Search Engine...")

        # 创建缓存配置
        cache_config = CacheConfig(
            cache_dir="cache",
            enable_cache=True,
            cache_ttl=86400  # 24小时
        )

        # 初始化缓存搜索引擎
        cached_engine = CachedSearchEngine(cache_config)

        initialization_status.update({
            "progress": 30,
            "message": "Preloading BERT models..."
        })

        # 预加载模型
        print("📥 Preloading models...")
        cached_engine.preload_models()

        initialization_status.update({
            "progress": 60,
            "message": "Loading data and building search index..."
        })

        # 加载数据并构建索引
        data_file = "split_files/USA_1_TOP3000.csv"
        if os.path.exists(data_file):
            print(f"📊 Loading data and building index...")
            cached_engine.load_data_and_build_index(data_file)

            initialization_status.update({
                "progress": 80,
                "message": "Initializing interactive engine..."
            })

            # 初始化交互式引擎（用于阈值调整）
            interactive_engine = InteractiveSearchEngine()
            interactive_engine.engine = cached_engine  # 使用缓存引擎作为后端

            data_loaded = True

            initialization_status.update({
                "status": "completed",
                "progress": 100,
                "message": "Search engines initialized successfully!"
            })

            print("✅ All search engines initialized successfully!")

        else:
            raise FileNotFoundError(f"Data file not found: {data_file}")

    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        initialization_status.update({
            "status": "error",
            "progress": 0,
            "message": f"Initialization failed: {str(e)}"
        })
        data_loaded = False

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        if not data_loaded:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized. Please check data file.'
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'Query cannot be empty'
            })
        
        # 获取或创建会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 执行搜索
        start_time = time.time()

        if cached_engine:
            # 使用缓存搜索引擎
            results = cached_engine.search(query, top_k=20, search_context=SearchContext.INTERACTIVE)
        else:
            # 回退到交互式引擎
            results = interactive_engine.handle_interaction(query, session_id) if interactive_engine else []

        search_time = time.time() - start_time

        # 获取会话统计（如果使用交互式引擎）
        stats = {}
        if interactive_engine and hasattr(interactive_engine, 'get_session_stats'):
            stats = interactive_engine.get_session_stats(session_id)
        else:
            # 使用缓存引擎的统计
            stats = {
                'current_thresholds': {'min_score': 0.01, 'semantic': 0.3, 'keyword': 0.2},
                'preference_score': 0.5,
                'threshold_trend': 'stable',
                'total_searches': 1
            }
        
        # 格式化结果
        formatted_results = []
        for result in results:
            formatted_results.append({
                'id': result.id,
                'description': result.description,
                'dataset_name': result.dataset_name,
                'category_name': result.category_name,
                'subcategory_name': result.subcategory_name,
                'region': result.region,
                'universe': result.universe,
                'delay': result.delay,
                'overall_score': round(result.overall_score, 4),
                'semantic_score': round(result.semantic_score, 4),
                'keyword_score': round(result.keyword_score, 4),
                'matched_columns': result.matched_columns,
                'matched_keywords': result.matched_keywords[:10]  # 限制关键词数量
            })
        
        return jsonify({
            'success': True,
            'results': formatted_results,
            'search_time': round(search_time, 3),
            'total_results': len(formatted_results),
            'session_stats': {
                'current_thresholds': stats.get('current_thresholds', {}),
                'preference_score': round(stats.get('preference_score', 0.5), 2),
                'threshold_trend': stats.get('threshold_trend', 'stable'),
                'total_searches': stats.get('total_searches', 0)
            }
        })
        
    except Exception as e:
        print(f"❌ Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/adjust_threshold', methods=['POST'])
def api_adjust_threshold():
    """阈值调整API"""
    try:
        if not data_loaded:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })
        
        data = request.get_json()
        action = data.get('action', '').strip().lower()
        
        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 映射动作到指令
        action_mapping = {
            'more_results': '更多结果',
            'more_precise': '更精准',
            'reset': '重置'
        }
        
        if action not in action_mapping:
            return jsonify({
                'success': False,
                'error': f'Invalid action: {action}'
            })
        
        # 执行阈值调整
        command = action_mapping[action]
        results = search_engine.handle_interaction(command, session_id)
        
        # 获取更新后的统计信息
        stats = search_engine.get_session_stats(session_id)
        
        # 格式化结果（如果有重新搜索）
        formatted_results = []
        if results:
            for result in results:
                formatted_results.append({
                    'id': result.id,
                    'description': result.description,
                    'dataset_name': result.dataset_name,
                    'category_name': result.category_name,
                    'subcategory_name': result.subcategory_name,
                    'overall_score': round(result.overall_score, 4),
                    'semantic_score': round(result.semantic_score, 4),
                    'keyword_score': round(result.keyword_score, 4),
                    'matched_columns': result.matched_columns,
                    'matched_keywords': result.matched_keywords[:10]
                })
        
        return jsonify({
            'success': True,
            'action': action,
            'results': formatted_results,
            'session_stats': {
                'current_thresholds': stats.get('current_thresholds', {}),
                'preference_score': round(stats.get('preference_score', 0.5), 2),
                'threshold_trend': stats.get('threshold_trend', 'stable'),
                'total_searches': stats.get('total_searches', 0)
            }
        })
        
    except Exception as e:
        print(f"❌ Threshold adjustment error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/status')
def api_status():
    """系统状态API"""
    return jsonify({
        'success': True,
        'data_loaded': data_loaded,
        'cached_engine_ready': cached_engine is not None,
        'interactive_engine_ready': interactive_engine is not None,
        'initialization_status': initialization_status,
        'session_id': session.get('session_id'),
        'timestamp': time.time()
    })

@app.route('/api/performance')
def api_performance():
    """性能统计API"""
    try:
        if cached_engine:
            stats = cached_engine.get_performance_stats()
            return jsonify({
                'success': True,
                'performance_stats': stats
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Cached engine not available'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/stats')
def api_stats():
    """获取详细统计信息"""
    try:
        session_id = session.get('session_id')
        if not session_id or not search_engine:
            return jsonify({
                'success': False,
                'error': 'No active session'
            })
        
        stats = search_engine.get_session_stats(session_id)
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    import threading

    # 在后台线程中初始化搜索引擎
    def background_init():
        initialize_search_engines()

    init_thread = threading.Thread(target=background_init)
    init_thread.daemon = True
    init_thread.start()

    # 启动Flask应用
    print("🌐 Starting Optimized BERT Fusion Search Web App...")
    print("📱 Access the app at: http://localhost:5000")
    print("⚡ Features: Model Preloading + Vector Caching + Index Persistence")

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,  # 关闭debug模式以提高性能
        threaded=True,
        use_reloader=False  # 避免重复初始化
    )
