#!/usr/bin/env python3
"""
缓存优化的搜索引擎
集成模型预加载、向量缓存、索引持久化等性能优化功能
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import os

# 导入优化的模型管理器
from optimized_model_manager import OptimizedModelManager, CacheConfig
from single_bert_fusion_engine import SearchResult, EnhancedChineseNLP
from dynamic_threshold_manager import (
    DynamicThresholdManager, SearchContext, SearchMetrics
)
from sklearn.metrics.pairwise import cosine_similarity

@dataclass
class IndexedDocument:
    """索引文档"""
    doc_id: int
    column_features: Dict[str, Any]
    row_data: Dict[str, Any]

class CachedSearchEngine:
    """缓存优化的搜索引擎"""
    
    def __init__(self, cache_config: CacheConfig = None):
        """初始化缓存搜索引擎"""
        print("🚀 Initializing Cached Search Engine...")
        
        # 配置
        self.cache_config = cache_config or CacheConfig()
        
        # 优化的模型管理器
        self.model_manager = OptimizedModelManager(self.cache_config)
        
        # 阈值管理器
        self.threshold_manager = DynamicThresholdManager()
        
        # 数据存储
        self.df = None
        self.indexed_documents = {}
        self.data_hash = None
        
        # 列权重
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
        
        # 性能统计
        self.search_stats = {
            'total_searches': 0,
            'cache_hits': 0,
            'avg_search_time': 0.0,
            'index_build_time': 0.0
        }
        
        print("✅ Cached Search Engine initialized")
    
    def preload_models(self, model_configs: Dict[str, str] = None):
        """预加载模型"""
        print("📥 Preloading models for search engine...")
        self.model_manager.preload_models(model_configs)
        print("✅ Models preloaded successfully")
    
    def load_data_and_build_index(self, file_path: str, force_rebuild: bool = False):
        """加载数据并构建索引"""
        print(f"📊 Loading data from {file_path}...")
        
        # 加载数据
        self.df = pd.read_csv(file_path)
        self.data_hash = self.model_manager._get_data_hash(self.df)
        
        print(f"✅ Loaded {len(self.df)} records")
        print(f"🔑 Data hash: {self.data_hash[:8]}...")
        
        # 尝试加载缓存的索引
        if not force_rebuild:
            cached_index = self.model_manager.load_index(self.data_hash)
            if cached_index:
                print("📦 Loading index from cache...")
                self.indexed_documents = cached_index
                print(f"✅ Index loaded from cache ({len(self.indexed_documents)} documents)")
                return
        
        # 构建新索引
        print("🔨 Building new search index...")
        self._build_search_index()
        
        # 保存索引到缓存
        self.model_manager.save_index(self.indexed_documents, self.data_hash)
        
        print("✅ Search index built and cached successfully")
    
    def _build_search_index(self):
        """构建搜索索引"""
        start_time = time.time()
        
        target_columns = list(self.column_weights.keys())
        
        # 收集所有需要编码的文本
        all_texts = []
        text_to_doc_col = {}  # 映射文本到(doc_id, col_name)
        
        print("📝 Collecting texts for batch encoding...")
        
        for idx, row in self.df.iterrows():
            if idx % 1000 == 0:
                print(f"   Processing row {idx}/{len(self.df)}")
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    all_texts.append(text_content)
                    text_to_doc_col[text_content] = (idx, col_name)
        
        print(f"📦 Batch encoding {len(all_texts)} texts...")
        
        # 批量编码所有文本
        all_vectors = self.model_manager.batch_encode_texts(all_texts)
        
        # 构建文档索引
        print("🏗️ Building document index...")
        
        for idx, row in self.df.iterrows():
            if idx % 1000 == 0:
                print(f"   Indexing document {idx}/{len(self.df)}")
            
            column_features = {}
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # 获取预编码的向量
                    text_index = all_texts.index(text_content)
                    vector = all_vectors[text_index]
                    
                    # NLP分析
                    keywords = self.model_manager.chinese_nlp.extract_keywords(text_content, top_k=15)
                    entities = self.model_manager.chinese_nlp.extract_entities(text_content)
                    
                    column_features[col_name] = {
                        'text': text_content,
                        'keywords': keywords,
                        'entities': entities,
                        'vector': vector
                    }
            
            # 创建索引文档
            self.indexed_documents[idx] = IndexedDocument(
                doc_id=idx,
                column_features=column_features,
                row_data=row.to_dict()
            )
        
        build_time = time.time() - start_time
        self.search_stats['index_build_time'] = build_time
        
        print(f"✅ Search index built in {build_time:.2f}s")
    
    def search(self, query: str, top_k: int = 10, 
               search_context: SearchContext = SearchContext.INTERACTIVE,
               user_preferences: Dict[str, Any] = None) -> List[SearchResult]:
        """执行搜索"""
        if not self.indexed_documents:
            raise ValueError("Search index not built. Please load data first.")
        
        print(f"🔍 Searching for: '{query}'")
        start_time = time.time()
        
        self.search_stats['total_searches'] += 1
        
        # 获取动态阈值
        adaptive_thresholds = self.threshold_manager.get_adaptive_thresholds(
            query, search_context, user_preferences
        )
        query_type = self.threshold_manager.classify_query_type(query)
        
        print(f"🎛️ Query type: {query_type.value}")
        print(f"🎯 Thresholds - Min: {adaptive_thresholds.min_score_threshold:.3f}, "
              f"Semantic: {adaptive_thresholds.semantic_threshold:.3f}")
        
        # 查询预处理
        query_keywords = self.model_manager.chinese_nlp.extract_keywords(query, top_k=15)
        
        # 编码查询（使用缓存）
        query_vector = self.model_manager.encode_text_cached(query)
        
        # 搜索所有文档
        search_results = []
        
        for doc_idx, indexed_doc in self.indexed_documents.items():
            column_features = indexed_doc.column_features
            row_data = indexed_doc.row_data
            
            # 计算分数
            column_scores = {}
            overall_semantic_score = 0.0
            overall_keyword_score = 0.0
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []
            
            for col_name, col_feature in column_features.items():
                # 关键词匹配分数
                keyword_score = self._calculate_keyword_similarity(
                    query_keywords, col_feature['keywords']
                )
                
                # 语义相似度分数
                semantic_score = self._calculate_semantic_similarity(
                    query_vector, col_feature['vector']
                )
                
                # 综合列分数
                col_score = keyword_score * 0.4 + semantic_score * 0.6
                column_scores[col_name] = col_score
                
                # 累积分数
                col_weight = self.column_weights[col_name]
                overall_semantic_score += semantic_score * col_weight
                overall_keyword_score += keyword_score * col_weight
                overall_score += col_score * col_weight
                
                # 记录匹配信息
                if (col_score > adaptive_thresholds.min_score_threshold and
                    (semantic_score > adaptive_thresholds.semantic_threshold or 
                     keyword_score > adaptive_thresholds.keyword_threshold)):
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw, weight in col_feature['keywords'][:5]])
            
            # 创建搜索结果
            if (overall_score > adaptive_thresholds.min_score_threshold and
                (overall_semantic_score > adaptive_thresholds.semantic_threshold or
                 overall_keyword_score > adaptive_thresholds.keyword_threshold)):
                
                result = SearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),
                    
                    overall_score=overall_score,
                    semantic_score=overall_semantic_score,
                    keyword_score=overall_keyword_score,
                    column_scores=column_scores,
                    
                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='cached_bert_fusion'
                )
                
                search_results.append(result)
        
        # 排序并返回结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)
        target_results = min(adaptive_thresholds.target_results, top_k)
        final_results = search_results[:target_results]
        
        search_time = time.time() - start_time
        
        # 更新统计
        self.search_stats['avg_search_time'] = (
            (self.search_stats['avg_search_time'] * (self.search_stats['total_searches'] - 1) + search_time) /
            self.search_stats['total_searches']
        )
        
        # 记录搜索指标
        if final_results:
            scores = [r.overall_score for r in final_results]
            metrics = SearchMetrics(
                query=query,
                query_type=query_type,
                search_context=search_context,
                total_results=len(final_results),
                avg_score=np.mean(scores),
                max_score=max(scores),
                min_score=min(scores),
                search_time=search_time,
                applied_thresholds={
                    'min_score': adaptive_thresholds.min_score_threshold,
                    'semantic': adaptive_thresholds.semantic_threshold,
                    'keyword': adaptive_thresholds.keyword_threshold
                }
            )
            self.threshold_manager.update_search_metrics(metrics)
        
        print(f"✅ Search completed in {search_time:.3f}s, found {len(final_results)} results")
        
        return final_results
    
    def _calculate_semantic_similarity(self, query_vector: np.ndarray, 
                                     doc_vector: np.ndarray) -> float:
        """计算语义相似度"""
        try:
            similarity = cosine_similarity(
                query_vector.reshape(1, -1), 
                doc_vector.reshape(1, -1)
            )[0][0]
            return float(similarity)
        except:
            return 0.0
    
    def _calculate_keyword_similarity(self, query_keywords: List[Tuple[str, float]], 
                                    doc_keywords: List[Tuple[str, float]]) -> float:
        """计算关键词相似度"""
        if not query_keywords or not doc_keywords:
            return 0.0
        
        query_dict = {word.lower(): weight for word, weight in query_keywords}
        doc_dict = {word.lower(): weight for word, weight in doc_keywords}
        
        score = 0.0
        total_query_weight = sum(query_dict.values())
        
        for word, weight in query_dict.items():
            if word in doc_dict:
                score += min(weight, doc_dict[word]) * 2.0
            else:
                # 部分匹配
                for doc_word in doc_dict:
                    if word in doc_word or doc_word in word:
                        score += weight * 0.8
                        break
        
        return score / (total_query_weight + 1e-8)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        model_stats = self.model_manager.get_performance_stats()
        cache_sizes = self.model_manager.get_cache_size()
        
        return {
            'search_stats': self.search_stats,
            'model_stats': model_stats,
            'cache_sizes': cache_sizes,
            'indexed_documents': len(self.indexed_documents),
            'data_hash': self.data_hash
        }
    
    def cleanup_cache(self, max_age_hours: int = 24):
        """清理缓存"""
        self.model_manager.cleanup_cache(max_age_hours)

if __name__ == "__main__":
    # 测试缓存搜索引擎
    print("🧪 Testing Cached Search Engine")
    print("=" * 60)
    
    # 初始化引擎
    engine = CachedSearchEngine()
    
    # 预加载模型
    engine.preload_models()
    
    # 加载数据并构建索引
    engine.load_data_and_build_index("split_files/USA_1_TOP3000.csv")
    
    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share", 
        "分析师预测",
        "EBITDA相关指标"
    ]
    
    print(f"\n🔍 Testing search performance...")
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        
        start_time = time.time()
        results = engine.search(query, top_k=5)
        search_time = time.time() - start_time
        
        print(f"   ⏱️  Search time: {search_time:.3f}s")
        print(f"   📊 Results: {len(results)}")
        
        for i, result in enumerate(results[:3], 1):
            print(f"   {i}. {result.id} (Score: {result.overall_score:.3f})")
    
    # 显示性能统计
    print(f"\n📈 Performance Statistics:")
    stats = engine.get_performance_stats()
    
    print(f"Search Stats:")
    for key, value in stats['search_stats'].items():
        print(f"   {key}: {value}")
    
    print(f"Cache Sizes:")
    for key, size in stats['cache_sizes'].items():
        print(f"   {key}: {size:.2f} MB")
    
    print(f"Model Stats:")
    model_stats = stats['model_stats']
    print(f"   Cache hit rate: {model_stats['cache_hit_rate']:.1%}")
    print(f"   Total requests: {model_stats['total_requests']}")
    print(f"   Avg encoding time: {model_stats['avg_encoding_time']:.3f}s")
    
    print(f"\n🎉 Test completed!")
