#!/usr/bin/env python3
"""
测试新的评分机制
验证完全匹配能得到最高分
"""

from fast_search_engine import FastSearchEngine
from optimized_model_manager import CacheConfig
import time

def test_scoring_mechanism():
    """测试评分机制"""
    print("🧪 Testing New Scoring Mechanism")
    print("=" * 60)
    
    # 初始化引擎
    engine = FastSearchEngine()
    engine.preload_models_fast()
    engine.load_data_and_build_index_fast('split_files/USA_1_TOP3000.csv', sample_size=2000)
    
    # 测试查询
    test_queries = [
        "earnings per share",
        "EBITDA",
        "revenue growth",
        "analyst estimate"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing query: '{query}'")
        print("-" * 50)
        
        start_time = time.time()
        results = engine.search(query, top_k=10)
        search_time = time.time() - start_time
        
        print(f"⏱️  Search time: {search_time:.3f}s")
        print(f"📊 Top results:")
        
        for i, result in enumerate(results[:5], 1):
            print(f"\n{i}. ID: {result.id}")
            print(f"   Description: {result.description[:80]}...")
            print(f"   Overall Score: {result.overall_score:.4f}")
            print(f"   Semantic Score: {result.semantic_score:.4f}")
            print(f"   Keyword Score: {result.keyword_score:.4f}")
            
            # 显示各字段的详细分数
            if hasattr(result, 'column_scores') and result.column_scores:
                print(f"   Field Scores:")
                for field, scores in result.column_scores.items():
                    print(f"     {field}: semantic={scores['semantic']:.3f}, keyword={scores['keyword']:.3f}, final={scores['final']:.3f}")
            
            print(f"   Matched Columns: {result.matched_columns}")
    
    print(f"\n🎉 Scoring test completed!")

if __name__ == "__main__":
    test_scoring_mechanism()
