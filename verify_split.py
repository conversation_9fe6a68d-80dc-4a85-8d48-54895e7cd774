#!/usr/bin/env python3
"""
Verify that the split was done correctly by checking a few sample files
"""

import pandas as pd
import os

def verify_split(split_dir='split_files'):
    """Verify that the split files contain the correct data"""
    
    print("VERIFYING SPLIT FILES...")
    print("=" * 50)
    
    # Check a few sample files
    sample_files = [
        'USA_1_TOP3000.csv',
        'EUR_1_TOP1200.csv', 
        'CHN_1_TOP2000U.csv',
        'GLB_1_MINVOL1M.csv'
    ]
    
    for filename in sample_files:
        filepath = os.path.join(split_dir, filename)
        if not os.path.exists(filepath):
            print(f"❌ {filename} not found!")
            continue
            
        print(f"\n📁 Checking {filename}...")
        
        # Parse expected values from filename
        parts = filename.replace('.csv', '').split('_')
        expected_region = parts[0]
        expected_delay = parts[1]
        expected_universe = '_'.join(parts[2:])
        
        # Read and check the file
        try:
            df = pd.read_csv(filepath)
            
            # Check if all rows have the expected values
            unique_regions = df['region'].unique()
            unique_delays = df['delay'].unique()
            unique_universes = df['universe'].unique()
            
            print(f"   Records: {len(df)}")
            print(f"   Regions: {unique_regions}")
            print(f"   Delays: {unique_delays}")
            print(f"   Universes: {unique_universes}")
            
            # Verify correctness
            if (len(unique_regions) == 1 and unique_regions[0] == expected_region and
                len(unique_delays) == 1 and str(unique_delays[0]) == expected_delay and
                len(unique_universes) == 1 and unique_universes[0] == expected_universe):
                print("   ✅ CORRECT - All records match expected values")
            else:
                print("   ❌ ERROR - Found unexpected values!")
                
        except Exception as e:
            print(f"   ❌ ERROR reading file: {e}")
    
    print(f"\n" + "=" * 50)
    print("Verification complete!")

if __name__ == "__main__":
    verify_split()
