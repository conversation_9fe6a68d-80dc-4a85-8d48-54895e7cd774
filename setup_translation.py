#!/usr/bin/env python3
"""
设置翻译环境
下载并安装Argos Translate的英中翻译包
"""

import argostranslate.package
import argostranslate.translate

def setup_argos_translation():
    """设置Argos翻译"""
    print("🚀 Setting up Argos Translation...")
    
    # 更新包列表
    print("📦 Updating package list...")
    argostranslate.package.update_package_index()
    
    # 获取可用包
    available_packages = argostranslate.package.get_available_packages()
    print(f"📋 Found {len(available_packages)} available packages")
    
    # 查找英中翻译包
    en_zh_packages = [
        pkg for pkg in available_packages 
        if pkg.from_code == 'en' and pkg.to_code == 'zh'
    ]
    
    if not en_zh_packages:
        print("❌ No English-Chinese translation package found")
        return False
    
    # 安装英中翻译包
    for pkg in en_zh_packages:
        print(f"📥 Installing {pkg.from_code}-{pkg.to_code} package...")
        try:
            argostranslate.package.install_from_path(pkg.download())
            print(f"✅ Successfully installed {pkg.from_code}-{pkg.to_code} package")
        except Exception as e:
            print(f"❌ Failed to install package: {e}")
            return False
    
    # 验证安装
    installed_packages = argostranslate.package.get_installed_packages()
    en_zh_installed = any(
        pkg.from_code == 'en' and pkg.to_code == 'zh' 
        for pkg in installed_packages
    )
    
    if en_zh_installed:
        print("✅ English-Chinese translation package installed successfully")
        
        # 测试翻译
        print("🧪 Testing translation...")
        test_text = "earnings per share"
        translated = argostranslate.translate.translate(test_text, 'en', 'zh')
        print(f"   '{test_text}' → '{translated}'")
        
        return True
    else:
        print("❌ Installation verification failed")
        return False

if __name__ == "__main__":
    success = setup_argos_translation()
    if success:
        print("🎉 Translation setup completed successfully!")
    else:
        print("💥 Translation setup failed!")
