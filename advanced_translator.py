#!/usr/bin/env python3
"""
高级翻译系统
集成Argos Translate开源神经网络翻译 + 本地专业词典
"""

import os
import time
import pickle
from typing import Dict, Optional, List
from pathlib import Path
import threading
import queue

# 导入Argos Translate
try:
    import argostranslate.package
    import argostranslate.translate
    ARGOS_AVAILABLE = True
    print("✅ Argos Translate available")
except ImportError:
    ARGOS_AVAILABLE = False
    print("⚠️ Argos Translate not available")

# 导入本地翻译字典
from simple_translator import SimpleTranslator

class AdvancedTranslator:
    """高级翻译系统"""
    
    def __init__(self, cache_dir: str = "cache/translation"):
        """初始化高级翻译器"""
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 翻译缓存
        self.cache_file = self.cache_dir / "translation_cache.pkl"
        self.cache = self._load_cache()
        
        # 本地字典翻译器
        self.local_translator = SimpleTranslator()
        
        # Argos翻译器状态
        self.argos_ready = False
        self.argos_translator = None
        
        # 翻译队列（用于批量翻译）
        self.translation_queue = queue.Queue()
        self.batch_results = {}
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'local_dict_hits': 0,
            'argos_translations': 0,
            'failed_translations': 0
        }
        
        print("🚀 Advanced Translator initialized")
        
        # 在后台初始化Argos翻译器
        if ARGOS_AVAILABLE:
            threading.Thread(target=self._init_argos_translator, daemon=True).start()
    
    def _load_cache(self) -> Dict[str, str]:
        """加载翻译缓存"""
        if self.cache_file.exists():
            try:
                import builtins
                with builtins.open(self.cache_file, 'rb') as f:
                    cache = pickle.load(f)
                print(f"📦 Loaded {len(cache)} cached translations")
                return cache
            except Exception as e:
                print(f"⚠️ Failed to load cache: {e}")
        return {}
    
    def _save_cache(self):
        """保存翻译缓存"""
        try:
            import builtins
            with builtins.open(self.cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
        except Exception as e:
            print(f"⚠️ Failed to save cache: {e}")
    
    def _init_argos_translator(self):
        """初始化Argos翻译器"""
        try:
            print("📥 Initializing Argos Translate...")
            
            # 检查是否已安装英中翻译包
            available_packages = argostranslate.package.get_available_packages()
            en_zh_packages = [
                pkg for pkg in available_packages 
                if pkg.from_code == 'en' and pkg.to_code == 'zh'
            ]
            
            if not en_zh_packages:
                print("📦 Installing English-Chinese translation package...")
                # 下载并安装英中翻译包
                for pkg in available_packages:
                    if pkg.from_code == 'en' and pkg.to_code == 'zh':
                        argostranslate.package.install_from_path(pkg.download())
                        break
            
            # 检查已安装的包
            installed_packages = argostranslate.package.get_installed_packages()
            en_zh_installed = any(
                pkg.from_code == 'en' and pkg.to_code == 'zh' 
                for pkg in installed_packages
            )
            
            if en_zh_installed:
                self.argos_ready = True
                print("✅ Argos Translate ready for English-Chinese translation")
            else:
                print("⚠️ English-Chinese package not available")
                
        except Exception as e:
            print(f"❌ Argos Translate initialization failed: {e}")
    
    def translate_to_chinese(self, text: str, field_type: str = 'description', 
                           use_neural: bool = True) -> str:
        """翻译英文到中文"""
        if not text or not isinstance(text, str):
            return text
        
        self.stats['total_requests'] += 1
        
        # 生成缓存键
        cache_key = f"{field_type}:{text}"
        
        # 1. 检查缓存
        if cache_key in self.cache:
            self.stats['cache_hits'] += 1
            return self.cache[cache_key]
        
        # 2. 基本检查
        if text.isdigit() or len(text) <= 2:
            return text
        
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return text
        
        # 3. 优先尝试Argos神经网络翻译（对于描述类文本）
        if use_neural and self.argos_ready and ARGOS_AVAILABLE and field_type == 'description':
            try:
                neural_result = argostranslate.translate.translate(text, 'en', 'zh')
                if neural_result and neural_result != text:
                    # 后处理：结合本地词典优化翻译结果
                    optimized_result = self._optimize_translation(neural_result, field_type)
                    self.stats['argos_translations'] += 1
                    self.cache[cache_key] = optimized_result
                    return optimized_result
            except Exception as e:
                print(f"⚠️ Argos translation failed for '{text}': {e}")

        # 4. 尝试本地字典翻译
        local_result = self.local_translator.translate_to_chinese(text, field_type)
        if local_result != text:  # 如果本地翻译有效果
            self.stats['local_dict_hits'] += 1
            self.cache[cache_key] = local_result
            return local_result

        # 5. 对于非描述类文本，再尝试神经网络翻译
        if use_neural and self.argos_ready and ARGOS_AVAILABLE and field_type != 'description':
            try:
                neural_result = argostranslate.translate.translate(text, 'en', 'zh')
                if neural_result and neural_result != text:
                    optimized_result = self._optimize_translation(neural_result, field_type)
                    self.stats['argos_translations'] += 1
                    self.cache[cache_key] = optimized_result
                    return optimized_result
            except Exception as e:
                print(f"⚠️ Argos translation failed for '{text}': {e}")
        
        # 6. 翻译失败，返回原文
        self.stats['failed_translations'] += 1
        self.cache[cache_key] = text
        return text
    
    def _optimize_translation(self, neural_result: str, field_type: str) -> str:
        """优化神经网络翻译结果"""
        optimized = neural_result.strip()

        # 专业术语替换字典
        financial_terms = {
            # 基础指标
            'earnings per share': '每股收益',
            'return on equity': '净资产收益率',
            'return on assets': '总资产收益率',
            'price to earnings': '市盈率',
            'price to book': '市净率',
            'debt to equity': '资产负债率',
            'current ratio': '流动比率',
            'quick ratio': '速动比率',
            'gross margin': '毛利率',
            'net margin': '净利率',
            'operating margin': '营业利润率',
            'EBITDA margin': 'EBITDA利润率',

            # 时间相关
            'trailing twelve months': '过去十二个月',
            'forward looking': '前瞻性',
            'quarterly': '季度',
            'annual': '年度',
            'monthly': '月度',

            # 分析师相关
            'consensus': '一致预期',
            'estimate': '预测',
            'revision': '修正',
            'target': '目标',
            'upside': '上涨空间',
            'recommendation': '建议',

            # 增长相关
            'growth': '增长',
            'revenue growth': '营收增长',
            'earnings growth': '盈利增长'
        }

        # 常见翻译错误修正
        corrections = {
            '每股盈利': '每股收益',
            '每股收入': '每股收益',
            '股权收益': '净资产收益率',
            '分析员': '分析师',
            '预估': '预测',
            '估计': '预测',
            '收入': '营收',
            '共识': '一致预期',
            '倒置': '上涨空间',
            '低于': '过去',
            '订正': '修正',
            '数': '',  # 移除多余的"数"字
            '个月': '个月',
            'EBITDA': 'EBITDA'
        }

        # 应用修正
        for wrong, correct in corrections.items():
            optimized = optimized.replace(wrong, correct)

        # 清理多余空格
        optimized = ' '.join(optimized.split())

        # 如果翻译结果太短或包含太多英文，使用本地字典翻译
        if len(optimized) < 3 or sum(1 for c in optimized if c.isalpha() and ord(c) < 128) > len(optimized) * 0.5:
            local_result = self.local_translator.translate_to_chinese(neural_result, field_type)
            if local_result != neural_result:
                return local_result

        return optimized
    
    def batch_translate(self, texts: List[str], field_type: str = 'description') -> List[str]:
        """批量翻译"""
        results = []
        uncached_texts = []
        uncached_indices = []
        
        # 检查缓存
        for i, text in enumerate(texts):
            cache_key = f"{field_type}:{text}"
            if cache_key in self.cache:
                results.append(self.cache[cache_key])
                self.stats['cache_hits'] += 1
            else:
                results.append(None)
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # 批量翻译未缓存的文本
        if uncached_texts:
            print(f"🔄 Batch translating {len(uncached_texts)} texts...")
            
            for i, text in enumerate(uncached_texts):
                translated = self.translate_to_chinese(text, field_type)
                result_index = uncached_indices[i]
                results[result_index] = translated
        
        return results
    
    def get_translation_stats(self) -> Dict[str, any]:
        """获取翻译统计"""
        total = self.stats['total_requests']
        cache_hit_rate = self.stats['cache_hits'] / total if total > 0 else 0
        
        return {
            'total_requests': total,
            'cache_hits': self.stats['cache_hits'],
            'cache_hit_rate': cache_hit_rate,
            'local_dict_hits': self.stats['local_dict_hits'],
            'argos_translations': self.stats['argos_translations'],
            'failed_translations': self.stats['failed_translations'],
            'argos_ready': self.argos_ready,
            'cache_size': len(self.cache)
        }
    
    def preload_common_terms(self):
        """预加载常用术语翻译"""
        common_terms = [
            # 财务指标
            "earnings per share", "return on equity", "return on assets",
            "price to earnings", "price to book", "debt to equity",
            "current ratio", "quick ratio", "gross margin", "net margin",
            "operating margin", "revenue growth", "earnings growth",
            
            # 分析师相关
            "analyst estimate", "analyst rating", "consensus estimate",
            "price target", "recommendation", "upgrade", "downgrade",
            "buy", "sell", "hold", "strong buy", "strong sell",
            
            # 数据集类型
            "broker estimates", "fundamental analysis", "technical analysis",
            "market data", "financial statements", "economic indicators",
            
            # 时间相关
            "quarterly", "annual", "monthly", "daily", "trailing",
            "forward", "current", "next", "previous", "actual", "estimate"
        ]
        
        print(f"🔄 Preloading {len(common_terms)} common terms...")
        
        for term in common_terms:
            self.translate_to_chinese(term, 'description')
        
        # 保存缓存
        self._save_cache()
        print(f"✅ Preloaded translations cached")
    
    def cleanup_cache(self, max_size: int = 10000):
        """清理缓存"""
        if len(self.cache) > max_size:
            # 保留最近使用的翻译
            print(f"🧹 Cleaning cache (current size: {len(self.cache)})")
            # 这里可以实现LRU清理逻辑
            # 简单实现：随机删除一些条目
            import random
            keys_to_remove = random.sample(list(self.cache.keys()), len(self.cache) - max_size)
            for key in keys_to_remove:
                del self.cache[key]
            
            self._save_cache()
            print(f"✅ Cache cleaned (new size: {len(self.cache)})")
    
    def __del__(self):
        """析构函数，保存缓存"""
        self._save_cache()

if __name__ == "__main__":
    # 测试高级翻译器
    print("🧪 Testing Advanced Translator")
    print("=" * 60)
    
    # 初始化翻译器
    translator = AdvancedTranslator()
    
    # 等待Argos初始化
    print("⏳ Waiting for Argos initialization...")
    time.sleep(5)
    
    # 预加载常用术语
    translator.preload_common_terms()
    
    # 测试翻译
    test_cases = [
        ("earnings per share", "description"),
        ("Broker Estimates", "dataset"),
        ("Analyst", "category"),
        ("quarterly revenue growth estimate", "description"),
        ("consensus price target", "description"),
        ("return on equity ratio", "description")
    ]
    
    print(f"\n🔍 Testing translations...")
    for text, field_type in test_cases:
        start_time = time.time()
        translated = translator.translate_to_chinese(text, field_type)
        translation_time = time.time() - start_time
        
        print(f"{field_type:12} | {text:35} → {translated:20} ({translation_time:.3f}s)")
    
    # 显示统计信息
    print(f"\n📊 Translation Statistics:")
    stats = translator.get_translation_stats()
    for key, value in stats.items():
        if key == 'cache_hit_rate':
            print(f"   {key}: {value:.1%}")
        else:
            print(f"   {key}: {value}")
    
    print(f"\n🎉 Advanced translator test completed!")
