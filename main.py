#!/usr/bin/env python3
"""
中文精准模糊搜索系统 - 主程序
基于开源大语言模型架构实现的搜索系统
"""

import os
import sys
import argparse
import time
from typing import List, Dict, Any

def check_dependencies():
    """检查依赖包是否安装"""
    # 简化依赖检查，只检查核心包
    core_packages = ['pandas', 'numpy', 'jieba', 'flask']

    missing_packages = []
    for package in core_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ 缺少以下核心依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    print("✅ 核心依赖包已安装")
    return True

def preprocess_data(csv_file: str, output_dir: str = "processed_data"):
    """预处理数据"""
    print("🔄 开始数据预处理...")
    
    from data_preprocessor import DataPreprocessor
    
    if not os.path.exists(csv_file):
        print(f"❌ 数据文件不存在: {csv_file}")
        return False
    
    try:
        preprocessor = DataPreprocessor()
        df, embeddings = preprocessor.process_data(csv_file, output_dir)
        
        print(f"✅ 数据预处理完成!")
        print(f"   - 处理记录数: {len(df)}")
        print(f"   - 向量维度: {embeddings.shape[1]}")
        print(f"   - 输出目录: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据预处理失败: {e}")
        return False

def test_search_engines():
    """测试搜索引擎"""
    print("🧪 测试搜索引擎...")
    
    try:
        from hybrid_search_engine import HybridSearchEngine
        
        # 初始化搜索引擎
        engine = HybridSearchEngine()
        engine.load_processed_data()
        
        # 测试查询
        test_queries = [
            "earnings per share",
            "每股收益", 
            "EBITDA",
            "分析师估算",
            "act_12m_eps_value"
        ]
        
        print("测试查询结果:")
        for query in test_queries:
            print(f"\n🔍 查询: '{query}'")
            
            start_time = time.time()
            result = engine.smart_search(query, top_k=3)
            search_time = time.time() - start_time
            
            print(f"   搜索策略: {result['query_analysis']['search_strategy']}")
            print(f"   搜索时间: {search_time:.4f}秒")
            print(f"   结果数量: {len(result['results'])}")
            
            for i, res in enumerate(result['results'][:3]):
                print(f"   {i+1}. {res['id']} (分数: {res['final_score']:.4f})")
                print(f"      {res['description'][:80]}...")
        
        print("\n✅ 搜索引擎测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 搜索引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_api_server():
    """启动API服务器"""
    print("🚀 启动API服务器...")
    
    try:
        from search_api import app, initialize_engines
        
        # 初始化搜索引擎
        if not initialize_engines():
            print("❌ 搜索引擎初始化失败")
            return False
        
        print("✅ 搜索引擎初始化成功")
        print("🌐 API服务器启动中...")
        print("   访问地址: http://localhost:5000")
        print("   Web界面: http://localhost:5000")
        print("   API文档: http://localhost:5000/api/stats")
        
        app.run(host='0.0.0.0', port=5000, debug=False)
        
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        return False

def interactive_search():
    """交互式搜索"""
    print("💬 进入交互式搜索模式...")
    print("输入 'quit' 或 'exit' 退出")
    
    try:
        from hybrid_search_engine import HybridSearchEngine
        
        # 初始化搜索引擎
        engine = HybridSearchEngine()
        engine.load_processed_data()
        
        print("✅ 搜索引擎加载完成!")
        
        while True:
            try:
                query = input("\n🔍 请输入搜索查询: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                
                if not query:
                    continue
                
                # 执行搜索
                start_time = time.time()
                result = engine.smart_search(query, top_k=5)
                search_time = time.time() - start_time
                
                # 显示结果
                print(f"\n📊 搜索结果 (耗时: {search_time:.4f}秒)")
                print(f"策略: {result['query_analysis']['search_strategy']}")
                print("-" * 80)
                
                if not result['results']:
                    print("未找到相关结果")
                    continue
                
                for res in result['results']:
                    print(f"{res['rank']}. {res['id']} (分数: {res['final_score']:.4f})")
                    print(f"   描述: {res['description']}")
                    print(f"   数据集: {res['dataset_name']}")
                    print(f"   类别: {res['category_name']} > {res['subcategory_name']}")
                    if 'search_methods' in res:
                        print(f"   搜索方法: {', '.join(res['search_methods'])}")
                    print()
                
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 搜索出错: {e}")
        
    except Exception as e:
        print(f"❌ 交互式搜索初始化失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='中文精准模糊搜索系统')
    parser.add_argument('command', choices=['preprocess', 'test', 'server', 'interactive'], 
                       help='执行的命令')
    parser.add_argument('--data', default='data.csv', help='数据文件路径')
    parser.add_argument('--output', default='processed_data', help='预处理输出目录')
    
    args = parser.parse_args()
    
    print("🔍 中文精准模糊搜索系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    if args.command == 'preprocess':
        # 数据预处理
        success = preprocess_data(args.data, args.output)
        sys.exit(0 if success else 1)
        
    elif args.command == 'test':
        # 测试搜索引擎
        if not os.path.exists(args.output):
            print(f"❌ 预处理数据不存在: {args.output}")
            print("请先运行: python main.py preprocess")
            sys.exit(1)
        
        success = test_search_engines()
        sys.exit(0 if success else 1)
        
    elif args.command == 'server':
        # 启动API服务器
        if not os.path.exists(args.output):
            print(f"❌ 预处理数据不存在: {args.output}")
            print("请先运行: python main.py preprocess")
            sys.exit(1)
        
        start_api_server()
        
    elif args.command == 'interactive':
        # 交互式搜索
        if not os.path.exists(args.output):
            print(f"❌ 预处理数据不存在: {args.output}")
            print("请先运行: python main.py preprocess")
            sys.exit(1)
        
        interactive_search()

if __name__ == "__main__":
    main()
