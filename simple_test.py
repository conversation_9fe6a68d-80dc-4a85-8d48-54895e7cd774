#!/usr/bin/env python3
"""
简化测试脚本 - 测试搜索系统的基本功能
"""

import pandas as pd
import numpy as np
import jieba
import re
from fuzzywuzzy import fuzz
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import time

class SimpleSearchEngine:
    """简化的搜索引擎，不依赖复杂的向量模型"""
    
    def __init__(self):
        self.df = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.search_fields = ['id', 'description', 'dataset.id', 'dataset.name', 
                             'category.id', 'category.name', 'subcategory.id', 'subcategory.name']
    
    def load_data(self, csv_file: str):
        """加载数据"""
        print(f"Loading data from {csv_file}...")
        self.df = pd.read_csv(csv_file)
        print(f"Loaded {len(self.df)} records")
        
        # 预处理数据
        self.preprocess_data()
        
        # 构建TF-IDF索引
        self.build_tfidf_index()
    
    def clean_text(self, text: str) -> str:
        """清洗文本"""
        if pd.isna(text) or text is None:
            return ""
        
        text = str(text)
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-_.,()%/]', ' ', text)
        return text.strip()
    
    def segment_text(self, text: str) -> str:
        """中文分词"""
        if not text:
            return ""
        
        words = jieba.lcut(text)
        filtered_words = [word for word in words 
                         if len(word) >= 2 and not word.isdigit() and word.strip()]
        return ' '.join(filtered_words)
    
    def preprocess_data(self):
        """预处理数据"""
        print("Preprocessing data...")
        
        # 创建搜索文本
        def create_searchable_text(row):
            texts = []
            for field in self.search_fields:
                if field in row and pd.notna(row[field]):
                    cleaned_text = self.clean_text(row[field])
                    if cleaned_text:
                        texts.append(cleaned_text)
            return " | ".join(texts)
        
        self.df['searchable_text'] = self.df.apply(create_searchable_text, axis=1)
        self.df['keywords'] = self.df['searchable_text'].apply(self.segment_text)
        
        print("Data preprocessing completed!")
    
    def build_tfidf_index(self):
        """构建TF-IDF索引"""
        print("Building TF-IDF index...")
        
        documents = self.df['keywords'].fillna('').tolist()
        
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.8
        )
        
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(documents)
        print(f"TF-IDF index built with {self.tfidf_matrix.shape[1]} features")
    
    def exact_search(self, query: str, top_k: int = 10):
        """精确搜索"""
        results = []
        query_lower = query.lower()
        
        for idx, row in self.df.iterrows():
            score = 0
            for field in self.search_fields:
                if field in row and pd.notna(row[field]):
                    field_value = str(row[field]).lower()
                    if query_lower in field_value:
                        if query_lower == field_value:
                            score += 100
                        elif field_value.startswith(query_lower):
                            score += 80
                        else:
                            score += 50
            
            if score > 0:
                results.append({
                    'rank': 0,
                    'score': score,
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_name': row.get('dataset.name', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'search_type': 'exact'
                })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        for i, result in enumerate(results[:top_k]):
            result['rank'] = i + 1
        
        return results[:top_k]
    
    def fuzzy_search(self, query: str, top_k: int = 10, threshold: int = 60):
        """模糊搜索"""
        results = []
        
        for idx, row in self.df.iterrows():
            max_score = 0
            
            for field in self.search_fields:
                if field in row and pd.notna(row[field]):
                    field_value = str(row[field])
                    score = fuzz.partial_ratio(query, field_value)
                    max_score = max(max_score, score)
            
            if max_score >= threshold:
                results.append({
                    'rank': 0,
                    'score': max_score,
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_name': row.get('dataset.name', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'search_type': 'fuzzy'
                })
        
        results.sort(key=lambda x: x['score'], reverse=True)
        for i, result in enumerate(results[:top_k]):
            result['rank'] = i + 1
        
        return results[:top_k]
    
    def tfidf_search(self, query: str, top_k: int = 10):
        """TF-IDF搜索"""
        if self.tfidf_vectorizer is None:
            return []
        
        processed_query = self.segment_text(self.clean_text(query))
        query_vector = self.tfidf_vectorizer.transform([processed_query])
        
        similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for i, idx in enumerate(top_indices):
            if similarities[idx] > 0:
                row = self.df.iloc[idx]
                results.append({
                    'rank': i + 1,
                    'score': float(similarities[idx]),
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_name': row.get('dataset.name', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'search_type': 'tfidf'
                })
        
        return results
    
    def hybrid_search(self, query: str, top_k: int = 10):
        """混合搜索"""
        # 执行多种搜索
        exact_results = self.exact_search(query, top_k)
        fuzzy_results = self.fuzzy_search(query, top_k)
        tfidf_results = self.tfidf_search(query, top_k)
        
        # 简单合并结果
        all_results = {}
        
        # 添加精确搜索结果
        for result in exact_results:
            item_id = result['id']
            if item_id not in all_results:
                all_results[item_id] = result.copy()
                all_results[item_id]['combined_score'] = result['score'] * 0.4
                all_results[item_id]['methods'] = ['exact']
            else:
                all_results[item_id]['combined_score'] += result['score'] * 0.4
                all_results[item_id]['methods'].append('exact')
        
        # 添加模糊搜索结果
        for result in fuzzy_results:
            item_id = result['id']
            if item_id not in all_results:
                all_results[item_id] = result.copy()
                all_results[item_id]['combined_score'] = result['score'] * 0.3
                all_results[item_id]['methods'] = ['fuzzy']
            else:
                all_results[item_id]['combined_score'] += result['score'] * 0.3
                all_results[item_id]['methods'].append('fuzzy')
        
        # 添加TF-IDF搜索结果
        for result in tfidf_results:
            item_id = result['id']
            if item_id not in all_results:
                all_results[item_id] = result.copy()
                all_results[item_id]['combined_score'] = result['score'] * 100 * 0.3
                all_results[item_id]['methods'] = ['tfidf']
            else:
                all_results[item_id]['combined_score'] += result['score'] * 100 * 0.3
                all_results[item_id]['methods'].append('tfidf')
        
        # 排序并返回结果
        final_results = list(all_results.values())
        final_results.sort(key=lambda x: x['combined_score'], reverse=True)
        
        for i, result in enumerate(final_results[:top_k]):
            result['rank'] = i + 1
            result['search_type'] = 'hybrid'
        
        return final_results[:top_k]

def test_search_engine():
    """测试搜索引擎"""
    print("🧪 测试简化搜索引擎...")
    
    # 初始化搜索引擎
    engine = SimpleSearchEngine()
    engine.load_data('data.csv')
    
    # 测试查询
    test_queries = [
        "earnings per share",
        "每股收益",
        "EBITDA", 
        "分析师",
        "act_12m_eps_value"
    ]
    
    print("\n" + "="*80)
    print("搜索测试结果")
    print("="*80)
    
    for query in test_queries:
        print(f"\n🔍 查询: '{query}'")
        print("-" * 60)
        
        start_time = time.time()
        results = engine.hybrid_search(query, top_k=5)
        search_time = time.time() - start_time
        
        print(f"搜索时间: {search_time:.4f}秒")
        print(f"结果数量: {len(results)}")
        
        for result in results:
            print(f"{result['rank']}. {result['id']} (分数: {result['combined_score']:.2f})")
            print(f"   描述: {result['description'][:80]}...")
            print(f"   数据集: {result['dataset_name']}")
            print(f"   搜索方法: {', '.join(result['methods'])}")
            print()

if __name__ == "__main__":
    test_search_engine()
