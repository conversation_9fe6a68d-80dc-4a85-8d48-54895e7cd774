#!/usr/bin/env python3
"""
多模型融合搜索引擎
集成BERT、RoBERTa、Sentence-BERT等模型，实现特征融合检索
专门优化中文支持能力
"""

import os
import time
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
import json
import pickle
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 尝试导入深度学习模型
try:
    import torch
    from transformers import (
        AutoTokenizer, AutoModel, 
        BertTokenizer, BertModel,
        RobertaTokenizer, RobertaModel
    )
    from sentence_transformers import SentenceTransformer
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    print("⚠️ Transformers not available, using fallback models")
    TRANSFORMERS_AVAILABLE = False

# 中文NLP工具
import jieba
import jieba.posseg as pseg
import re
from collections import defaultdict, Counter

class ModelType(Enum):
    """模型类型"""
    BERT_BASE_CHINESE = "bert-base-chinese"
    ROBERTA_WWM_EXT = "hfl/chinese-roberta-wwm-ext"
    SENTENCE_BERT = "paraphrase-multilingual-MiniLM-L12-v2"
    BGE_LARGE_ZH = "BAAI/bge-large-zh-v1.5"
    TEXT2VEC_CHINESE = "shibing624/text2vec-base-chinese"

@dataclass
class ColumnFeature:
    """列特征"""
    column_name: str
    text_content: str
    importance_weight: float
    feature_vector: Optional[np.ndarray] = None
    keywords: List[str] = None

@dataclass
class FusedSearchResult:
    """融合搜索结果"""
    id: str
    description: str
    dataset_name: str
    category_name: str
    subcategory_name: str
    region: str
    universe: str
    delay: str
    
    # 融合特征
    overall_score: float
    column_scores: Dict[str, float]
    model_scores: Dict[str, float]
    feature_weights: Dict[str, float]
    
    # 匹配信息
    matched_columns: List[str]
    matched_keywords: List[str]
    search_mode: str

class ChineseNLPProcessor:
    """增强的中文NLP处理器"""
    
    def __init__(self):
        self.financial_entities = self._load_financial_entities()
        self.stop_words = self._load_chinese_stopwords()
        self.synonym_dict = self._load_chinese_synonyms()
        
        # 初始化jieba
        self._init_jieba_dict()
        
        # 预编译正则表达式
        self.patterns = {
            'chinese_chars': re.compile(r'[\u4e00-\u9fff]+'),
            'english_words': re.compile(r'[a-zA-Z]+'),
            'numbers': re.compile(r'\d+\.?\d*'),
            'financial_ratios': re.compile(r'(每股|净资产|总资产|营业|利润|收入|成本).{0,10}(率|比|额|值|收益)'),
            'time_expressions': re.compile(r'\d+[年月日季度]|[上下]半年|年初|年末'),
            'percentage': re.compile(r'\d+\.?\d*%'),
            'currency': re.compile(r'[¥$€£]\d+\.?\d*[万亿千百十]?')
        }
    
    def _load_financial_entities(self) -> Dict[str, List[str]]:
        """加载金融实体词典"""
        return {
            'financial_metrics': [
                '每股收益', 'EPS', '净资产收益率', 'ROE', '总资产收益率', 'ROA',
                '毛利率', '净利率', '营业利润率', '资产负债率', '流动比率', '速动比率',
                '市盈率', 'PE', '市净率', 'PB', '市销率', 'PS', 'EBITDA', 'EBIT',
                '营业收入', '净利润', '毛利润', '现金流', '自由现金流', '经营现金流'
            ],
            'analyst_terms': [
                '分析师', '研究员', '分析员', '研报', '评级', '目标价', '盈利预测',
                '一致预期', '买入', '卖出', '持有', '增持', '减持', '中性', '推荐'
            ],
            'time_periods': [
                '年报', '半年报', '季报', '月报', '年度', '季度', '月度',
                '同比', '环比', '年初', '年末', '季末', '月末'
            ],
            'industries': [
                '银行', '保险', '证券', '房地产', '制造业', '科技', '医药',
                '消费', '能源', '材料', '工业', '公用事业', '电信'
            ]
        }
    
    def _load_chinese_stopwords(self) -> set:
        """加载中文停用词"""
        return {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还是', '为了', '还有', '可以', '这个', '那个',
            '什么', '怎么', '为什么', '因为', '所以', '但是', '然后', '如果', '虽然', '虽说',
            '数据', '指标', '公司', '企业', '股票', '证券', '市场', '行业', '报告', '分析'
        }
    
    def _load_chinese_synonyms(self) -> Dict[str, List[str]]:
        """加载中文同义词词典"""
        return {
            '每股收益': ['EPS', '每股盈利', '每股净收益', '每股利润'],
            '净资产收益率': ['ROE', '股东权益回报率', '净资产报酬率', '权益回报率'],
            '总资产收益率': ['ROA', '资产回报率', '总资产报酬率', '资产收益率'],
            '市盈率': ['PE', 'P/E', '价格收益比', '市价盈利比'],
            '市净率': ['PB', 'P/B', '价格净值比', '市价净值比'],
            '营业收入': ['营收', '销售收入', '主营业务收入', '收入', '营业额'],
            '净利润': ['净收益', '税后利润', '净盈利', '归母净利润'],
            '毛利率': ['毛利润率', '销售毛利率', '毛利边际'],
            '分析师': ['研究员', '分析员', '证券分析师', '研究分析师'],
            '增长率': ['增长幅度', '增速', '涨幅', '成长率'],
            '现金流': ['现金流量', '资金流', '现金流动'],
            '资产负债率': ['负债率', '债务比率', '杠杆率'],
            '预测': ['预期', '预估', '估算', '预计', '展望']
        }
    
    def _init_jieba_dict(self):
        """初始化jieba词典"""
        # 添加金融实体到jieba词典
        for category, entities in self.financial_entities.items():
            for entity in entities:
                jieba.add_word(entity, freq=2000, tag=category)
        
        # 添加同义词
        for key, synonyms in self.synonym_dict.items():
            jieba.add_word(key, freq=1500, tag='financial')
            for synonym in synonyms:
                jieba.add_word(synonym, freq=1200, tag='financial')
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取命名实体"""
        entities = defaultdict(list)
        
        if not text:
            return entities
        
        # 使用正则表达式提取特定实体
        for pattern_name, pattern in self.patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[pattern_name].extend(matches)
        
        # 使用词典匹配提取金融实体
        words = jieba.lcut(text)
        for word in words:
            for category, entity_list in self.financial_entities.items():
                if word in entity_list:
                    entities[category].append(word)
        
        return dict(entities)
    
    def segment_with_pos(self, text: str) -> List[Tuple[str, str]]:
        """中文分词和词性标注"""
        if not text:
            return []
        
        words_with_pos = pseg.lcut(text)
        filtered_results = []
        
        for word, pos in words_with_pos:
            word = word.strip()
            if (len(word) >= 1 and 
                not word.isspace() and
                word not in self.stop_words):
                filtered_results.append((word, pos))
        
        return filtered_results
    
    def extract_keywords(self, text: str, top_k: int = 15) -> List[Tuple[str, float]]:
        """提取关键词和权重"""
        words_with_pos = self.segment_with_pos(text)
        word_weights = defaultdict(float)
        
        for word, pos in words_with_pos:
            weight = 1.0
            
            # 金融实体权重
            for category, entities in self.financial_entities.items():
                if word in entities:
                    weight *= 4.0
                    break
            
            # 词性权重
            if pos.startswith('n'):  # 名词
                weight *= 2.5
            elif pos.startswith('v'):  # 动词
                weight *= 1.8
            elif pos.startswith('a'):  # 形容词
                weight *= 1.5
            elif pos in ['m', 'q']:  # 数词、量词
                weight *= 2.0
            
            # 词长权重
            if len(word) >= 4:
                weight *= 2.0
            elif len(word) >= 3:
                weight *= 1.5
            elif len(word) >= 2:
                weight *= 1.2
            
            # 中英文混合权重
            if self.patterns['chinese_chars'].search(word) and self.patterns['english_words'].search(word):
                weight *= 1.8
            
            word_weights[word] += weight
        
        # 排序并返回top_k
        sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
        return sorted_words[:top_k]
    
    def expand_query(self, query: str) -> Dict[str, List[str]]:
        """智能查询扩展"""
        expanded = {
            'original': [query],
            'synonyms': [],
            'related_terms': [],
            'entities': []
        }
        
        # 分词
        words = [word for word, pos in self.segment_with_pos(query)]
        
        # 同义词扩展
        for word in words:
            if word in self.synonym_dict:
                expanded['synonyms'].extend(self.synonym_dict[word])
            
            # 反向查找同义词
            for key, synonyms in self.synonym_dict.items():
                if word in synonyms and key not in expanded['synonyms']:
                    expanded['synonyms'].append(key)
        
        # 实体扩展
        entities = self.extract_entities(query)
        for category, entity_list in entities.items():
            expanded['entities'].extend(entity_list)
        
        # 相关术语扩展
        for word in words:
            for category, entity_list in self.financial_entities.items():
                if word in entity_list:
                    # 添加同类别的相关术语
                    related = [e for e in entity_list if e != word][:3]
                    expanded['related_terms'].extend(related)
        
        # 去重
        for key in expanded:
            expanded[key] = list(set(expanded[key]))
        
        return expanded

class MultiModelFusionEngine:
    """多模型融合搜索引擎"""
    
    def __init__(self, model_configs: Optional[Dict[str, Any]] = None):
        """
        初始化多模型融合引擎
        
        Args:
            model_configs: 模型配置字典
        """
        self.model_configs = model_configs or self._get_default_model_configs()
        self.models = {}
        self.tokenizers = {}
        self.nlp_processor = ChineseNLPProcessor()
        
        # 数据存储
        self.df = None
        self.column_features = {}
        self.fusion_index = {}
        
        # 缓存
        self.embedding_cache = {}
        self.search_cache = {}
        
        print("🚀 Initializing Multi-Model Fusion Engine...")
    
    def _get_default_model_configs(self) -> Dict[str, Any]:
        """获取默认模型配置"""
        return {
            'chinese_bert': {
                'model_name': 'bert-base-chinese',
                'weight': 0.3,
                'enabled': True
            },
            'roberta_wwm': {
                'model_name': 'hfl/chinese-roberta-wwm-ext',
                'weight': 0.3,
                'enabled': True
            },
            'sentence_bert': {
                'model_name': 'paraphrase-multilingual-MiniLM-L12-v2',
                'weight': 0.4,
                'enabled': True
            }
        }
    
    def load_models(self):
        """加载所有配置的模型"""
        if not TRANSFORMERS_AVAILABLE:
            print("⚠️ Transformers not available, using simplified models")
            return self._load_simplified_models()
        
        print("📥 Loading pre-trained models...")
        
        for model_key, config in self.model_configs.items():
            if not config.get('enabled', True):
                continue
            
            model_name = config['model_name']
            print(f"   Loading {model_key}: {model_name}")
            
            try:
                if 'sentence' in model_key.lower():
                    # Sentence-BERT模型
                    model = SentenceTransformer(model_name)
                    self.models[model_key] = model
                else:
                    # BERT/RoBERTa模型
                    tokenizer = AutoTokenizer.from_pretrained(model_name)
                    model = AutoModel.from_pretrained(model_name)
                    
                    self.tokenizers[model_key] = tokenizer
                    self.models[model_key] = model
                
                print(f"   ✅ {model_key} loaded successfully")
                
            except Exception as e:
                print(f"   ❌ Failed to load {model_key}: {e}")
                config['enabled'] = False
        
        print(f"✅ Loaded {len(self.models)} models successfully")
    
    def _load_simplified_models(self):
        """加载简化模型（当transformers不可用时）"""
        print("📥 Loading simplified models...")
        
        # 使用简化的向量化方法
        from sklearn.feature_extraction.text import TfidfVectorizer
        
        self.models['tfidf'] = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 3),
            min_df=2,
            max_df=0.8
        )
        
        print("✅ Simplified models loaded")
    
    def encode_text_multi_model(self, text: str) -> Dict[str, np.ndarray]:
        """使用多个模型编码文本"""
        encodings = {}
        
        if not TRANSFORMERS_AVAILABLE:
            # 简化编码
            return {'tfidf': np.array([hash(text) % 1000])}
        
        for model_key, model in self.models.items():
            try:
                if 'sentence' in model_key.lower():
                    # Sentence-BERT编码
                    encoding = model.encode([text])[0]
                else:
                    # BERT/RoBERTa编码
                    tokenizer = self.tokenizers[model_key]
                    inputs = tokenizer(text, return_tensors='pt', 
                                     truncation=True, max_length=512, padding=True)
                    
                    with torch.no_grad():
                        outputs = model(**inputs)
                        # 使用[CLS]token的表示
                        encoding = outputs.last_hidden_state[:, 0, :].numpy()[0]
                
                encodings[model_key] = encoding
                
            except Exception as e:
                print(f"⚠️ Encoding failed for {model_key}: {e}")
        
        return encodings
    
    def extract_column_features(self, row: pd.Series) -> Dict[str, ColumnFeature]:
        """提取列特征"""
        target_columns = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
        
        column_features = {}
        
        for col_name, importance in target_columns.items():
            if col_name in row and pd.notna(row[col_name]):
                text_content = str(row[col_name])
                
                # 提取关键词
                keywords = [kw for kw, weight in self.nlp_processor.extract_keywords(text_content, top_k=10)]
                
                # 创建列特征
                feature = ColumnFeature(
                    column_name=col_name,
                    text_content=text_content,
                    importance_weight=importance,
                    keywords=keywords
                )
                
                # 生成特征向量
                if text_content.strip():
                    feature.feature_vector = self.encode_text_multi_model(text_content)
                
                column_features[col_name] = feature
        
        return column_features
    
    def build_fusion_index(self, df: pd.DataFrame):
        """构建融合索引"""
        print(f"🔨 Building fusion index for {len(df)} records...")
        
        self.df = df
        start_time = time.time()
        
        # 加载模型
        self.load_models()
        
        # 处理每一行数据
        for idx, row in df.iterrows():
            if idx % 1000 == 0:
                print(f"   Processing row {idx}/{len(df)}")
            
            # 提取列特征
            column_features = self.extract_column_features(row)
            
            # 存储特征
            self.fusion_index[idx] = {
                'column_features': column_features,
                'row_data': row.to_dict()
            }
        
        build_time = time.time() - start_time
        print(f"✅ Fusion index built in {build_time:.2f}s")
    
    def calculate_similarity_scores(self, query_encodings: Dict[str, np.ndarray], 
                                   doc_encodings: Dict[str, np.ndarray]) -> Dict[str, float]:
        """计算相似度分数"""
        similarity_scores = {}
        
        for model_key in query_encodings:
            if model_key in doc_encodings:
                try:
                    query_vec = query_encodings[model_key]
                    doc_vec = doc_encodings[model_key]
                    
                    # 计算余弦相似度
                    similarity = np.dot(query_vec, doc_vec) / (
                        np.linalg.norm(query_vec) * np.linalg.norm(doc_vec) + 1e-8
                    )
                    
                    similarity_scores[model_key] = float(similarity)
                    
                except Exception as e:
                    print(f"⚠️ Similarity calculation failed for {model_key}: {e}")
                    similarity_scores[model_key] = 0.0
        
        return similarity_scores
    
    def fusion_search(self, query: str, top_k: int = 10, 
                     column_weights: Optional[Dict[str, float]] = None) -> List[FusedSearchResult]:
        """执行融合搜索"""
        if not self.fusion_index:
            raise ValueError("Fusion index not built. Please build index first.")
        
        print(f"🔍 Performing fusion search for: '{query}'")
        start_time = time.time()
        
        # 查询预处理和扩展
        expanded_query = self.nlp_processor.expand_query(query)
        
        # 编码查询
        query_encodings = self.encode_text_multi_model(query)
        
        # 搜索结果
        search_results = []
        
        # 遍历所有文档
        for doc_idx, doc_data in self.fusion_index.items():
            column_features = doc_data['column_features']
            row_data = doc_data['row_data']
            
            # 计算每列的分数
            column_scores = {}
            overall_score = 0.0
            matched_columns = []
            matched_keywords = []
            
            for col_name, col_feature in column_features.items():
                col_score = 0.0
                
                # 关键词匹配分数
                keyword_score = self._calculate_keyword_score(
                    query, expanded_query, col_feature.keywords
                )
                
                # 语义相似度分数
                semantic_score = 0.0
                if col_feature.feature_vector:
                    model_similarities = self.calculate_similarity_scores(
                        query_encodings, col_feature.feature_vector
                    )
                    
                    # 加权平均语义分数
                    for model_key, similarity in model_similarities.items():
                        model_weight = self.model_configs.get(model_key, {}).get('weight', 0.33)
                        semantic_score += similarity * model_weight
                
                # 综合列分数
                col_score = keyword_score * 0.4 + semantic_score * 0.6
                column_scores[col_name] = col_score
                
                # 加权到总分
                col_weight = column_weights.get(col_name, col_feature.importance_weight) if column_weights else col_feature.importance_weight
                overall_score += col_score * col_weight
                
                # 记录匹配信息
                if col_score > 0.1:
                    matched_columns.append(col_name)
                    matched_keywords.extend([kw for kw in col_feature.keywords if kw.lower() in query.lower()])
            
            # 创建搜索结果
            if overall_score > 0.01:  # 过滤低分结果
                result = FusedSearchResult(
                    id=str(row_data.get('id', '')),
                    description=str(row_data.get('description', '')),
                    dataset_name=str(row_data.get('dataset.name', '')),
                    category_name=str(row_data.get('category.name', '')),
                    subcategory_name=str(row_data.get('subcategory.name', '')),
                    region=str(row_data.get('region', '')),
                    universe=str(row_data.get('universe', '')),
                    delay=str(row_data.get('delay', '')),
                    
                    overall_score=overall_score,
                    column_scores=column_scores,
                    model_scores={},  # 可以添加模型级别的分数
                    feature_weights=column_weights or {},
                    
                    matched_columns=matched_columns,
                    matched_keywords=list(set(matched_keywords)),
                    search_mode='fusion'
                )
                
                search_results.append(result)
        
        # 排序并返回top_k结果
        search_results.sort(key=lambda x: x.overall_score, reverse=True)
        
        search_time = time.time() - start_time
        print(f"✅ Search completed in {search_time:.3f}s, found {len(search_results)} results")
        
        return search_results[:top_k]
    
    def _calculate_keyword_score(self, query: str, expanded_query: Dict[str, List[str]], 
                                keywords: List[str]) -> float:
        """计算关键词匹配分数"""
        if not keywords:
            return 0.0
        
        score = 0.0
        query_lower = query.lower()
        keywords_lower = [kw.lower() for kw in keywords]
        
        # 原始查询匹配
        for keyword in keywords_lower:
            if query_lower in keyword or keyword in query_lower:
                if query_lower == keyword:
                    score += 1.0  # 完全匹配
                else:
                    score += 0.7  # 部分匹配
        
        # 扩展查询匹配
        for category, terms in expanded_query.items():
            weight = {'synonyms': 0.8, 'related_terms': 0.6, 'entities': 0.9}.get(category, 0.5)
            
            for term in terms:
                term_lower = term.lower()
                for keyword in keywords_lower:
                    if term_lower in keyword or keyword in term_lower:
                        score += weight * 0.5
        
        return min(score, 2.0)  # 限制最大分数

if __name__ == "__main__":
    # 示例用法
    engine = MultiModelFusionEngine()
    
    # 加载测试数据
    print("Loading test data...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv").head(100)  # 测试用100条数据
    
    # 构建融合索引
    engine.build_fusion_index(df)
    
    # 测试搜索
    test_queries = [
        "每股收益",
        "earnings per share", 
        "分析师预测",
        "EBITDA相关指标"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = engine.fusion_search(query, top_k=5)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.id} (Score: {result.overall_score:.4f})")
            print(f"   Description: {result.description[:80]}...")
            print(f"   Matched columns: {result.matched_columns}")
            print(f"   Keywords: {result.matched_keywords[:5]}")
            print()
