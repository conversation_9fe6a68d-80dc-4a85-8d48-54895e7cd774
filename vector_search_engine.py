#!/usr/bin/env python3
"""
向量搜索引擎 - 基于sentence-transformers实现语义相似度搜索
"""

import numpy as np
import pandas as pd
import faiss
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any, Tuple, Optional
import pickle
import os
from tqdm import tqdm

class VectorSearchEngine:
    """向量搜索引擎"""
    
    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        """
        初始化向量搜索引擎
        
        Args:
            model_name: 向量化模型名称
        """
        self.model_name = model_name
        self.model = None
        self.index = None
        self.df = None
        self.embeddings = None
        self.metadata = None
        
    def load_model(self):
        """加载向量化模型"""
        if self.model is None:
            print(f"Loading model: {self.model_name}")
            try:
                self.model = SentenceTransformer(self.model_name)
                print("Model loaded successfully!")
            except Exception as e:
                print(f"Failed to load model {self.model_name}, falling back to multilingual model")
                self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
    
    def load_processed_data(self, data_dir: str = "processed_data"):
        """
        加载预处理后的数据
        
        Args:
            data_dir: 数据目录
        """
        print("Loading processed data...")
        
        # 加载DataFrame
        df_path = os.path.join(data_dir, "processed_data.pkl")
        self.df = pd.read_pickle(df_path)
        
        # 加载向量
        embeddings_path = os.path.join(data_dir, "embeddings.npy")
        self.embeddings = np.load(embeddings_path)
        
        # 加载元数据
        metadata_path = os.path.join(data_dir, "metadata.pkl")
        with open(metadata_path, 'rb') as f:
            self.metadata = pickle.load(f)
        
        print(f"Loaded {len(self.df)} records with {self.embeddings.shape[1]}D embeddings")
        
        # 构建FAISS索引
        self.build_faiss_index()
    
    def build_faiss_index(self):
        """构建FAISS索引以加速搜索"""
        print("Building FAISS index...")
        
        # 标准化向量
        embeddings_normalized = self.embeddings.astype('float32')
        faiss.normalize_L2(embeddings_normalized)
        
        # 创建索引
        dimension = embeddings_normalized.shape[1]
        
        # 使用IVF索引以提高搜索速度
        if len(self.embeddings) > 1000:
            nlist = min(100, len(self.embeddings) // 10)  # 聚类中心数量
            quantizer = faiss.IndexFlatIP(dimension)  # 内积索引
            self.index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
            self.index.train(embeddings_normalized)
        else:
            # 对于小数据集，使用简单的平面索引
            self.index = faiss.IndexFlatIP(dimension)
        
        self.index.add(embeddings_normalized)
        print(f"FAISS index built with {self.index.ntotal} vectors")
    
    def encode_query(self, query: str) -> np.ndarray:
        """
        编码查询文本
        
        Args:
            query: 查询文本
            
        Returns:
            查询向量
        """
        if self.model is None:
            self.load_model()
        
        query_embedding = self.model.encode([query])
        query_embedding = query_embedding.astype('float32')
        faiss.normalize_L2(query_embedding)
        
        return query_embedding
    
    def vector_search(self, query: str, top_k: int = 10, 
                     score_threshold: float = 0.0) -> List[Dict[str, Any]]:
        """
        执行向量搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            score_threshold: 相似度阈值
            
        Returns:
            搜索结果列表
        """
        if self.index is None:
            raise ValueError("Index not built. Please load processed data first.")
        
        # 编码查询
        query_vector = self.encode_query(query)
        
        # 搜索
        scores, indices = self.index.search(query_vector, top_k)
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if score >= score_threshold:
                row = self.df.iloc[idx]
                result = {
                    'rank': i + 1,
                    'score': float(score),
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_id': row.get('dataset.id', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_id': row.get('category.id', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_id': row.get('subcategory.id', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'searchable_text': row['searchable_text'],
                    'search_type': 'vector'
                }
                results.append(result)
        
        return results
    
    def batch_search(self, queries: List[str], top_k: int = 10) -> List[List[Dict[str, Any]]]:
        """
        批量搜索
        
        Args:
            queries: 查询列表
            top_k: 每个查询返回的结果数量
            
        Returns:
            每个查询的搜索结果列表
        """
        results = []
        for query in tqdm(queries, desc="Batch searching"):
            query_results = self.vector_search(query, top_k)
            results.append(query_results)
        
        return results
    
    def find_similar_items(self, item_id: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        根据item ID查找相似项目
        
        Args:
            item_id: 项目ID
            top_k: 返回结果数量
            
        Returns:
            相似项目列表
        """
        # 找到对应的行
        matching_rows = self.df[self.df['id'] == item_id]
        if matching_rows.empty:
            return []
        
        row_idx = matching_rows.index[0]
        item_vector = self.embeddings[row_idx:row_idx+1].astype('float32')
        faiss.normalize_L2(item_vector)
        
        # 搜索相似项目
        scores, indices = self.index.search(item_vector, top_k + 1)  # +1 因为会包含自己
        
        results = []
        for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
            if idx != row_idx:  # 排除自己
                row = self.df.iloc[idx]
                result = {
                    'rank': len(results) + 1,
                    'score': float(score),
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_id': row.get('dataset.id', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_id': row.get('category.id', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_id': row.get('subcategory.id', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'searchable_text': row['searchable_text'],
                    'search_type': 'similarity'
                }
                results.append(result)
                
                if len(results) >= top_k:
                    break
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索引擎统计信息"""
        if self.df is None:
            return {}
        
        return {
            'total_records': len(self.df),
            'embedding_dimension': self.embeddings.shape[1] if self.embeddings is not None else 0,
            'model_name': self.model_name,
            'index_type': type(self.index).__name__ if self.index else None,
            'unique_datasets': self.df['dataset.name'].nunique() if 'dataset.name' in self.df.columns else 0,
            'unique_categories': self.df['category.name'].nunique() if 'category.name' in self.df.columns else 0,
            'unique_subcategories': self.df['subcategory.name'].nunique() if 'subcategory.name' in self.df.columns else 0
        }

if __name__ == "__main__":
    # 示例用法
    engine = VectorSearchEngine()
    
    # 加载数据
    engine.load_processed_data()
    
    # 测试搜索
    results = engine.vector_search("earnings per share", top_k=5)
    
    print("Search results:")
    for result in results:
        print(f"Rank {result['rank']}: {result['id']} - {result['description'][:100]}...")
        print(f"Score: {result['score']:.4f}")
        print("---")
