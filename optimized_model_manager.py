#!/usr/bin/env python3
"""
优化的模型管理器
实现模型预加载、向量缓存、索引持久化等性能优化功能
"""

import os
import time
import pickle
import joblib
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import hashlib
import json
from dataclasses import dataclass, asdict
import warnings
warnings.filterwarnings('ignore')

# 导入深度学习模型
try:
    import torch
    from sentence_transformers import SentenceTransformer
    MODELS_AVAILABLE = True
except ImportError:
    print("⚠️ Deep learning models not available")
    MODELS_AVAILABLE = False

# 导入中文NLP
from single_bert_fusion_engine import EnhancedChineseNLP

@dataclass
class CacheConfig:
    """缓存配置"""
    cache_dir: str = "cache"
    model_cache_dir: str = "cache/models"
    vector_cache_dir: str = "cache/vectors"
    index_cache_dir: str = "cache/indexes"
    enable_cache: bool = True
    cache_ttl: int = 86400  # 24小时
    max_cache_size: int = 1000  # MB

@dataclass
class ModelInfo:
    """模型信息"""
    name: str
    path: str
    load_time: float
    memory_usage: float
    last_used: float
    cache_hit_count: int = 0

class OptimizedModelManager:
    """优化的模型管理器"""
    
    def __init__(self, config: CacheConfig = None):
        self.config = config or CacheConfig()
        self.models = {}
        self.model_info = {}
        self.vector_cache = {}
        self.chinese_nlp = None
        
        # 创建缓存目录
        self._create_cache_dirs()
        
        # 性能统计
        self.stats = {
            'model_loads': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_encoding_time': 0.0,
            'total_search_time': 0.0
        }
        
        print(f"🚀 Optimized Model Manager initialized")
        print(f"📁 Cache directory: {self.config.cache_dir}")
    
    def _create_cache_dirs(self):
        """创建缓存目录"""
        dirs = [
            self.config.cache_dir,
            self.config.model_cache_dir,
            self.config.vector_cache_dir,
            self.config.index_cache_dir
        ]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def _get_cache_key(self, text: str, model_name: str) -> str:
        """生成缓存键"""
        content = f"{model_name}:{text}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_data_hash(self, df: pd.DataFrame) -> str:
        """生成数据哈希"""
        # 使用数据的形状和前几行内容生成哈希
        content = f"{df.shape}:{df.head().to_string()}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def preload_models(self, model_configs: Dict[str, str] = None):
        """预加载模型"""
        if not MODELS_AVAILABLE:
            print("⚠️ Deep learning models not available, skipping preload")
            return
        
        default_configs = {
            'sentence_bert': 'paraphrase-multilingual-MiniLM-L12-v2'
        }
        
        configs = model_configs or default_configs
        
        print("📥 Preloading models...")
        
        for model_key, model_path in configs.items():
            print(f"   Loading {model_key}: {model_path}")
            
            start_time = time.time()
            
            try:
                # 检查模型缓存
                cached_model_path = os.path.join(self.config.model_cache_dir, f"{model_key}.pkl")
                
                if os.path.exists(cached_model_path) and self.config.enable_cache:
                    print(f"   📦 Loading from cache...")
                    with open(cached_model_path, 'rb') as f:
                        model = pickle.load(f)
                    self.stats['cache_hits'] += 1
                else:
                    print(f"   🌐 Loading from HuggingFace...")
                    model = SentenceTransformer(model_path)
                    
                    # 保存到缓存
                    if self.config.enable_cache:
                        with open(cached_model_path, 'wb') as f:
                            pickle.dump(model, f)
                    
                    self.stats['cache_misses'] += 1
                
                load_time = time.time() - start_time
                
                # 估算内存使用
                memory_usage = self._estimate_model_memory(model)
                
                self.models[model_key] = model
                self.model_info[model_key] = ModelInfo(
                    name=model_key,
                    path=model_path,
                    load_time=load_time,
                    memory_usage=memory_usage,
                    last_used=time.time()
                )
                
                self.stats['model_loads'] += 1
                
                print(f"   ✅ {model_key} loaded in {load_time:.2f}s ({memory_usage:.1f}MB)")
                
            except Exception as e:
                print(f"   ❌ Failed to load {model_key}: {e}")
        
        # 预加载中文NLP处理器
        print("📝 Loading Chinese NLP processor...")
        self.chinese_nlp = EnhancedChineseNLP()
        print("✅ Chinese NLP processor loaded")
        
        print(f"🎉 Model preloading completed!")
    
    def _estimate_model_memory(self, model) -> float:
        """估算模型内存使用（MB）"""
        try:
            if hasattr(model, 'get_sentence_embedding_dimension'):
                # Sentence-BERT模型
                return 400.0  # 估算值
            else:
                return 200.0  # 默认估算值
        except:
            return 100.0
    
    def encode_text_cached(self, text: str, model_key: str = 'sentence_bert') -> np.ndarray:
        """带缓存的文本编码"""
        if model_key not in self.models:
            raise ValueError(f"Model {model_key} not loaded")
        
        # 检查向量缓存
        cache_key = self._get_cache_key(text, model_key)
        
        if self.config.enable_cache and cache_key in self.vector_cache:
            self.stats['cache_hits'] += 1
            return self.vector_cache[cache_key]
        
        # 检查磁盘缓存
        cache_file = os.path.join(self.config.vector_cache_dir, f"{cache_key}.npy")
        
        if self.config.enable_cache and os.path.exists(cache_file):
            try:
                vector = np.load(cache_file)
                self.vector_cache[cache_key] = vector
                self.stats['cache_hits'] += 1
                return vector
            except:
                pass
        
        # 编码文本
        start_time = time.time()
        
        model = self.models[model_key]
        vector = model.encode([text])[0]
        
        encoding_time = time.time() - start_time
        self.stats['total_encoding_time'] += encoding_time
        self.stats['cache_misses'] += 1
        
        # 更新模型使用时间
        self.model_info[model_key].last_used = time.time()
        self.model_info[model_key].cache_hit_count += 1
        
        # 保存到缓存
        if self.config.enable_cache:
            self.vector_cache[cache_key] = vector
            
            # 保存到磁盘
            try:
                np.save(cache_file, vector)
            except Exception as e:
                print(f"⚠️ Failed to save vector cache: {e}")
        
        return vector
    
    def batch_encode_texts(self, texts: List[str], model_key: str = 'sentence_bert') -> List[np.ndarray]:
        """批量编码文本（带缓存）"""
        if model_key not in self.models:
            raise ValueError(f"Model {model_key} not loaded")
        
        results = []
        uncached_texts = []
        uncached_indices = []
        
        # 检查缓存
        for i, text in enumerate(texts):
            cache_key = self._get_cache_key(text, model_key)
            
            if self.config.enable_cache and cache_key in self.vector_cache:
                results.append(self.vector_cache[cache_key])
                self.stats['cache_hits'] += 1
            else:
                # 检查磁盘缓存
                cache_file = os.path.join(self.config.vector_cache_dir, f"{cache_key}.npy")
                
                if self.config.enable_cache and os.path.exists(cache_file):
                    try:
                        vector = np.load(cache_file)
                        self.vector_cache[cache_key] = vector
                        results.append(vector)
                        self.stats['cache_hits'] += 1
                        continue
                    except:
                        pass
                
                # 需要编码
                results.append(None)
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # 批量编码未缓存的文本
        if uncached_texts:
            print(f"🔄 Encoding {len(uncached_texts)} uncached texts...")
            
            start_time = time.time()
            model = self.models[model_key]
            vectors = model.encode(uncached_texts)
            encoding_time = time.time() - start_time
            
            self.stats['total_encoding_time'] += encoding_time
            self.stats['cache_misses'] += len(uncached_texts)
            
            # 更新结果和缓存
            for i, (text, vector) in enumerate(zip(uncached_texts, vectors)):
                result_index = uncached_indices[i]
                results[result_index] = vector
                
                # 保存到缓存
                if self.config.enable_cache:
                    cache_key = self._get_cache_key(text, model_key)
                    self.vector_cache[cache_key] = vector
                    
                    # 保存到磁盘
                    cache_file = os.path.join(self.config.vector_cache_dir, f"{cache_key}.npy")
                    try:
                        np.save(cache_file, vector)
                    except Exception as e:
                        print(f"⚠️ Failed to save vector cache: {e}")
        
        return results
    
    def save_index(self, index_data: Dict[str, Any], data_hash: str, index_name: str = "main"):
        """保存索引到磁盘"""
        if not self.config.enable_cache:
            return
        
        index_file = os.path.join(self.config.index_cache_dir, f"{index_name}_{data_hash}.pkl")
        
        try:
            print(f"💾 Saving index to {index_file}...")
            
            # 添加元数据
            save_data = {
                'index_data': index_data,
                'data_hash': data_hash,
                'created_time': time.time(),
                'version': '1.0'
            }
            
            with open(index_file, 'wb') as f:
                pickle.dump(save_data, f, protocol=pickle.HIGHEST_PROTOCOL)
            
            print(f"✅ Index saved successfully")
            
        except Exception as e:
            print(f"❌ Failed to save index: {e}")
    
    def load_index(self, data_hash: str, index_name: str = "main") -> Optional[Dict[str, Any]]:
        """从磁盘加载索引"""
        if not self.config.enable_cache:
            return None
        
        index_file = os.path.join(self.config.index_cache_dir, f"{index_name}_{data_hash}.pkl")
        
        if not os.path.exists(index_file):
            return None
        
        try:
            print(f"📦 Loading index from {index_file}...")
            
            with open(index_file, 'rb') as f:
                save_data = pickle.load(f)
            
            # 检查缓存是否过期
            if time.time() - save_data.get('created_time', 0) > self.config.cache_ttl:
                print(f"⏰ Index cache expired, removing...")
                os.remove(index_file)
                return None
            
            print(f"✅ Index loaded successfully")
            return save_data['index_data']
            
        except Exception as e:
            print(f"❌ Failed to load index: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
        cache_hit_rate = self.stats['cache_hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'model_loads': self.stats['model_loads'],
            'total_requests': total_requests,
            'cache_hits': self.stats['cache_hits'],
            'cache_misses': self.stats['cache_misses'],
            'cache_hit_rate': cache_hit_rate,
            'avg_encoding_time': self.stats['total_encoding_time'] / max(self.stats['cache_misses'], 1),
            'total_encoding_time': self.stats['total_encoding_time'],
            'total_search_time': self.stats['total_search_time'],
            'loaded_models': list(self.models.keys()),
            'model_info': {k: asdict(v) for k, v in self.model_info.items()}
        }
    
    def cleanup_cache(self, max_age_hours: int = 24):
        """清理过期缓存"""
        if not self.config.enable_cache:
            return
        
        print(f"🧹 Cleaning up cache older than {max_age_hours} hours...")
        
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        # 清理向量缓存
        vector_cache_dir = Path(self.config.vector_cache_dir)
        if vector_cache_dir.exists():
            for cache_file in vector_cache_dir.glob("*.npy"):
                if current_time - cache_file.stat().st_mtime > max_age_seconds:
                    cache_file.unlink()
        
        # 清理索引缓存
        index_cache_dir = Path(self.config.index_cache_dir)
        if index_cache_dir.exists():
            for cache_file in index_cache_dir.glob("*.pkl"):
                if current_time - cache_file.stat().st_mtime > max_age_seconds:
                    cache_file.unlink()
        
        # 清理内存缓存
        self.vector_cache.clear()
        
        print(f"✅ Cache cleanup completed")
    
    def get_cache_size(self) -> Dict[str, float]:
        """获取缓存大小（MB）"""
        def get_dir_size(path: str) -> float:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    total_size += os.path.getsize(filepath)
            return total_size / (1024 * 1024)  # Convert to MB
        
        return {
            'vector_cache': get_dir_size(self.config.vector_cache_dir),
            'index_cache': get_dir_size(self.config.index_cache_dir),
            'model_cache': get_dir_size(self.config.model_cache_dir),
            'total_cache': get_dir_size(self.config.cache_dir)
        }

if __name__ == "__main__":
    # 测试优化的模型管理器
    print("🧪 Testing Optimized Model Manager")
    print("=" * 50)
    
    # 初始化管理器
    config = CacheConfig(enable_cache=True)
    manager = OptimizedModelManager(config)
    
    # 预加载模型
    manager.preload_models()
    
    # 测试编码
    test_texts = [
        "每股收益",
        "earnings per share",
        "分析师预测",
        "EBITDA相关指标"
    ]
    
    print(f"\n🔍 Testing text encoding...")
    for text in test_texts:
        start_time = time.time()
        vector = manager.encode_text_cached(text)
        encoding_time = time.time() - start_time
        
        print(f"   '{text}' -> {vector.shape} ({encoding_time:.3f}s)")
    
    # 测试批量编码
    print(f"\n📦 Testing batch encoding...")
    start_time = time.time()
    vectors = manager.batch_encode_texts(test_texts)
    batch_time = time.time() - start_time
    
    print(f"   Batch encoded {len(vectors)} texts in {batch_time:.3f}s")
    
    # 显示性能统计
    print(f"\n📊 Performance Statistics:")
    stats = manager.get_performance_stats()
    for key, value in stats.items():
        if key != 'model_info':
            print(f"   {key}: {value}")
    
    # 显示缓存大小
    print(f"\n💾 Cache Size:")
    cache_sizes = manager.get_cache_size()
    for key, size in cache_sizes.items():
        print(f"   {key}: {size:.2f} MB")
    
    print(f"\n🎉 Test completed!")
