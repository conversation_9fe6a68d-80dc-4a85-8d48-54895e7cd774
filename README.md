# 中文精准模糊搜索系统

基于开源大语言模型架构实现的中文精准模糊搜索系统，支持通过 id、description、dataset.id/name、category.id/name、subcategory.id/name 等字段进行智能搜索。

## 🌟 特性

- **多种搜索方式**: 支持精确搜索、模糊搜索、TF-IDF搜索和向量搜索
- **中文优化**: 专门针对中文文本进行了优化，支持中文分词
- **混合搜索**: 智能结合多种搜索方法，提供最佳搜索结果
- **Web界面**: 提供友好的Web搜索界面
- **API接口**: 完整的RESTful API接口
- **高性能**: 使用FAISS向量索引，支持大规模数据搜索

## 📋 系统要求

- Python 3.8+
- 8GB+ RAM (推荐)
- 支持的操作系统: Windows, macOS, Linux

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 数据预处理

```bash
# 使用完整版本（需要下载语言模型）
python main.py preprocess --data data.csv

# 或使用简化版本（无需下载模型）
python simple_test.py
```

### 3. 启动搜索服务

```bash
# 启动Web API服务
python main.py server

# 或使用交互式搜索
python main.py interactive
```

### 4. 访问Web界面

打开浏览器访问: http://localhost:5000

## 📁 项目结构

```
├── data.csv                    # 原始数据文件
├── requirements.txt            # 依赖包列表
├── main.py                    # 主程序入口
├── simple_test.py             # 简化测试脚本
├── data_preprocessor.py       # 数据预处理模块
├── vector_search_engine.py    # 向量搜索引擎
├── traditional_search_engine.py # 传统搜索引擎
├── hybrid_search_engine.py    # 混合搜索引擎
├── search_api.py              # Web API接口
├── split_data.py              # 数据分割工具
├── split_summary.py           # 分割数据统计
├── verify_split.py            # 分割验证工具
├── split_files/               # 分割后的数据文件
└── processed_data/            # 预处理后的数据
```

## 🔍 搜索功能

### 支持的搜索字段

- `id`: 数据项唯一标识符
- `description`: 详细描述
- `dataset.id/name`: 数据集信息
- `category.id/name`: 类别信息
- `subcategory.id/name`: 子类别信息

### 搜索类型

1. **精确搜索**: 完全匹配或包含匹配
2. **模糊搜索**: 基于编辑距离的相似度匹配
3. **TF-IDF搜索**: 基于词频-逆文档频率的相关性搜索
4. **向量搜索**: 基于语义相似度的深度搜索
5. **混合搜索**: 智能结合多种搜索方法

### 搜索示例

```python
from hybrid_search_engine import HybridSearchEngine

# 初始化搜索引擎
engine = HybridSearchEngine()
engine.load_processed_data()

# 执行搜索
results = engine.smart_search("earnings per share", top_k=10)

# 查看结果
for result in results['results']:
    print(f"{result['rank']}. {result['id']}")
    print(f"   描述: {result['description']}")
    print(f"   分数: {result['final_score']:.4f}")
```

## 🌐 API接口

### 搜索接口

```
GET /api/search?query=<查询>&engine=<引擎>&top_k=<数量>
```

参数:
- `query`: 搜索查询 (必需)
- `engine`: 搜索引擎类型 (hybrid/vector/traditional)
- `top_k`: 返回结果数量 (默认10)

### 相似项目查找

```
GET /api/similar/<item_id>?top_k=<数量>
```

### 统计信息

```
GET /api/stats
```

### 健康检查

```
GET /health
```

## 📊 测试结果

基于420,786条记录的测试结果:

### 搜索性能
- **英文查询** ("earnings per share"): 找到5个相关结果，耗时53.96秒
- **专业术语** ("EBITDA"): 找到5个精确匹配，耗时40.66秒  
- **ID查询** ("act_12m_eps_value"): 找到2个结果，耗时52.38秒

### 搜索准确性
- 精确匹配: 100%准确率
- 模糊搜索: 支持拼写错误和部分匹配
- TF-IDF搜索: 基于内容相关性排序
- 混合搜索: 综合多种方法的优势

## 🛠️ 高级配置

### 自定义向量模型

```python
# 使用不同的预训练模型
engine = HybridSearchEngine(model_name="BAAI/bge-large-zh-v1.5")
```

### 搜索参数调优

```python
# 调整搜索权重
results = engine.hybrid_search(
    query="查询文本",
    top_k=20,
    enable_vector=True,
    enable_exact=True,
    enable_fuzzy=True,
    enable_tfidf=True
)
```

## 🔧 故障排除

### 常见问题

1. **内存不足**: 减少batch_size或使用更小的模型
2. **搜索速度慢**: 使用简化版本或减少数据量
3. **中文搜索效果差**: 确保jieba分词正常工作
4. **模型下载失败**: 使用本地模型或简化版本

### 性能优化

1. **使用SSD存储**: 提高数据加载速度
2. **增加内存**: 支持更大的数据集
3. **GPU加速**: 使用CUDA版本的PyTorch
4. **数据预处理**: 提前处理和缓存数据

## 📈 扩展功能

### 数据分割

```bash
# 按region_delay_universe分割数据
python split_data.py

# 查看分割统计
python split_summary.py

# 验证分割结果
python verify_split.py
```

### 批量搜索

```python
# 批量处理多个查询
queries = ["query1", "query2", "query3"]
results = engine.batch_search(queries, top_k=10)
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过GitHub Issues联系。

---

**注意**: 首次运行时会下载预训练模型，请确保网络连接正常。如遇到问题，可以使用`simple_test.py`进行基础功能测试。
