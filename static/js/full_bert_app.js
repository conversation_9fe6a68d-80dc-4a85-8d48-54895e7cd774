/**
 * 完整BERT模式前端应用
 * 发挥BERT完整能力，预计算向量，极速响应
 */

class FullBertSearchApp {
    constructor() {
        this.currentQuery = '';
        this.isSearching = false;
        this.allResults = [];
        this.displayedResults = 0;
        this.resultsPerPage = 50;
        this.isDataLoaded = false;
        this.vectorCount = 0;
        
        this.initializeEventListeners();
        this.checkSystemStatus();
    }

    initializeEventListeners() {
        // 搜索相关事件
        document.getElementById('search-btn').addEventListener('click', () => {
            this.performSearch();
        });

        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.performSearch();
            }
        });

        // 阈值调整按钮事件
        document.getElementById('more-results-btn').addEventListener('click', () => {
            this.adjustThreshold('more_results');
        });

        document.getElementById('more-precise-btn').addEventListener('click', () => {
            this.adjustThreshold('more_precise');
        });

        document.getElementById('reset-btn').addEventListener('click', () => {
            this.adjustThreshold('reset');
        });

        // 数据集加载按钮事件
        document.getElementById('load-dataset-btn').addEventListener('click', () => {
            this.loadDataset();
        });
    }

    async checkSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            if (status.status === 'completed' && status.data_loaded) {
                this.isDataLoaded = true;
                this.hideLoading();
                this.showSuccessMessage('完整BERT模式已就绪！');
            } else if (status.status === 'initializing') {
                this.showLoading(status.message, status.progress);
                // 继续检查状态
                setTimeout(() => this.checkSystemStatus(), 2000);
            } else {
                this.hideLoading();
            }
        } catch (error) {
            console.error('Status check failed:', error);
            this.hideLoading();
        }
    }

    async loadDataset() {
        const select = document.getElementById('dataset-select');
        const filename = select.value;
        
        if (!filename) {
            this.showError('请选择一个数据集');
            return;
        }

        const loadBtn = document.getElementById('load-dataset-btn');
        const originalText = loadBtn.innerHTML;
        
        try {
            loadBtn.disabled = true;
            loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';
            
            this.showLoading('加载数据集...', 0);

            const response = await fetch('/api/load_dataset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ filename })
            });

            const data = await response.json();

            if (data.success) {
                this.isDataLoaded = true;
                this.hideLoading();
                this.showSuccessMessage(`数据集加载成功！已加载 ${data.records_loaded} 条记录`);
                
                // 更新向量计数显示
                this.vectorCount = data.records_loaded * 5; // 估算向量数量
                document.getElementById('vector-count').textContent = this.vectorCount.toLocaleString();
            } else {
                this.hideLoading();
                this.showError(data.error || '数据集加载失败');
            }

        } catch (error) {
            console.error('Dataset loading failed:', error);
            this.hideLoading();
            this.showError('数据集加载失败，请重试');
        } finally {
            loadBtn.disabled = false;
            loadBtn.innerHTML = originalText;
        }
    }

    async performSearch() {
        if (!this.isDataLoaded) {
            this.showError('请先加载数据集');
            return;
        }

        const query = document.getElementById('search-input').value.trim();
        if (!query) {
            this.showError('请输入搜索关键词');
            return;
        }

        if (this.isSearching) {
            return;
        }

        this.currentQuery = query;
        this.isSearching = true;

        const searchBtn = document.getElementById('search-btn');
        const originalText = searchBtn.innerHTML;

        try {
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>搜索中...';

            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query })
            });

            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data);
            } else {
                this.showError(data.error || '搜索失败');
            }

        } catch (error) {
            console.error('Search failed:', error);
            this.showError('搜索失败，请重试');
        } finally {
            this.isSearching = false;
            searchBtn.disabled = false;
            searchBtn.innerHTML = originalText;
        }
    }

    displaySearchResults(data) {
        this.allResults = data.results;
        this.displayedResults = 0;

        // 隐藏初始状态，显示结果
        document.getElementById('initial-state').style.display = 'none';
        document.getElementById('results-container').style.display = 'block';
        document.getElementById('details-initial-state').style.display = 'none';
        document.getElementById('details-container').style.display = 'block';

        // 显示性能统计
        const perfStats = document.getElementById('performance-stats');
        perfStats.style.display = 'block';
        document.getElementById('search-time').textContent = `${data.search_time}s`;
        document.getElementById('total-results').textContent = data.total_results.toLocaleString();
        document.getElementById('vector-count').textContent = this.vectorCount.toLocaleString();

        // 显示结果总览
        let overviewHtml = `
            <div class="card">
                <div class="card-header">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-brain me-2 text-primary"></i>
                            完整BERT搜索结果 (${data.results.length} 条)
                            <span class="badge bg-success ms-2">完整模式</span>
                        </h5>
                        <small class="text-muted">查询: "${this.currentQuery}" | 点击 <i class="fas fa-language text-primary"></i> 翻译单个因子</small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="results-list">
                        <!-- 结果将在这里动态加载 -->
                    </div>
                    ${data.results.length > this.resultsPerPage ? `
                    <div class="card-footer text-center">
                        <button class="btn btn-outline-primary" id="load-more-btn">
                            <i class="fas fa-chevron-down me-1"></i>
                            加载更多结果
                        </button>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.getElementById('results-container').innerHTML = overviewHtml;

        // 加载第一页结果
        this.loadMoreResults();

        // 绑定加载更多按钮事件
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMoreResults();
            });
        }

        // 默认显示第一个结果的详情
        if (data.results.length > 0) {
            this.showResultDetails(data.results[0], 1);
        }
    }

    loadMoreResults() {
        const resultsList = document.getElementById('results-list');
        if (!resultsList) return;

        const startIndex = this.displayedResults;
        const endIndex = Math.min(startIndex + this.resultsPerPage, this.allResults.length);

        for (let i = startIndex; i < endIndex; i++) {
            const result = this.allResults[i];
            const index = i;

            // 计算分数颜色
            const score = result.overall_score;
            let scoreColor = 'secondary';
            if (score >= 0.8) scoreColor = 'success';
            else if (score >= 0.6) scoreColor = 'primary';
            else if (score >= 0.4) scoreColor = 'warning';

            const itemHtml = `
                <div class="list-group-item list-group-item-action result-overview-item compact-item"
                     data-index="${index}"
                     style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1 me-2">
                            <div class="fw-bold text-primary small">${result.id}</div>
                            <div class="text-muted" style="font-size: 0.75rem; line-height: 1.2;">
                                ${result.dataset_name_translated || result.dataset_name}
                            </div>
                        </div>
                        <div class="text-end d-flex align-items-center">
                            <button class="btn btn-outline-primary btn-sm me-2 translate-single-btn" 
                                    data-index="${index}" 
                                    style="font-size: 0.7rem; padding: 0.2rem 0.4rem;">
                                <i class="fas fa-language"></i>
                            </button>
                            <div>
                                <span class="badge bg-${scoreColor}" style="font-size: 0.7rem;">
                                    ${(result.overall_score * 100).toFixed(0)}%
                                </span>
                                <div class="text-muted" style="font-size: 0.65rem;">#${index + 1}</div>
                                <div class="text-success" style="font-size: 0.6rem;">BERT</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            resultsList.insertAdjacentHTML('beforeend', itemHtml);
        }

        this.displayedResults = endIndex;

        // 绑定新添加项目的点击事件
        const newItems = resultsList.querySelectorAll('.result-overview-item:not(.bound)');
        newItems.forEach(item => {
            item.classList.add('bound');
            item.addEventListener('click', (e) => {
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.showResultDetails(this.allResults[index], index + 1);

                // 更新选中状态
                document.querySelectorAll('.result-overview-item').forEach(i => i.classList.remove('active'));
                e.currentTarget.classList.add('active');
            });
        });

        // 绑定翻译按钮事件
        const newTranslateButtons = resultsList.querySelectorAll('.translate-single-btn:not(.bound)');
        newTranslateButtons.forEach(button => {
            button.classList.add('bound');
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                this.translateSingleResult(index);
            });
        });

        // 更新加载更多按钮
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            if (this.displayedResults >= this.allResults.length) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.innerHTML = `
                    <i class="fas fa-chevron-down me-1"></i>
                    加载更多结果 (${this.displayedResults}/${this.allResults.length})
                `;
            }
        }
    }

    async translateSingleResult(index) {
        const result = this.allResults[index];
        const translateBtn = document.querySelector(`[data-index="${index}"].translate-single-btn`);
        
        if (!result || !translateBtn) return;
        
        // 如果已经翻译过，切换显示状态
        if (result.description_translated) {
            result.showTranslation = !result.showTranslation;
            this.updateTranslateButtonState(translateBtn, result);
            
            // 如果当前选中的是这个结果，更新详情显示
            const activeItem = document.querySelector('.result-overview-item.active');
            if (activeItem && parseInt(activeItem.dataset.index) === index) {
                this.showResultDetails(result, index + 1);
            }
            return;
        }
        
        // 执行翻译
        const originalHtml = translateBtn.innerHTML;
        
        try {
            translateBtn.disabled = true;
            translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    texts: [result.description]
                })
            });
            
            const data = await response.json();
            
            if (data.success && data.translations.length > 0) {
                // 保存翻译结果
                result.description_translated = data.translations[0];
                result.showTranslation = true;
                
                // 更新按钮状态
                this.updateTranslateButtonState(translateBtn, result);
                
                // 如果当前选中的是这个结果，更新详情显示
                const activeItem = document.querySelector('.result-overview-item.active');
                if (activeItem && parseInt(activeItem.dataset.index) === index) {
                    this.showResultDetails(result, index + 1);
                }
                
                this.showSuccessMessage(`翻译完成！用时 ${data.translation_time}s`);
            } else {
                this.showError(data.error || '翻译失败');
            }
            
        } catch (error) {
            console.error('Translation failed:', error);
            this.showError('翻译失败，请重试');
        } finally {
            translateBtn.disabled = false;
            if (!result.description_translated) {
                translateBtn.innerHTML = originalHtml;
            }
        }
    }

    updateTranslateButtonState(button, result) {
        if (result.description_translated) {
            if (result.showTranslation) {
                button.innerHTML = '<i class="fas fa-eye-slash"></i>';
                button.className = 'btn btn-outline-secondary btn-sm me-2 translate-single-btn';
                button.title = '显示原文';
            } else {
                button.innerHTML = '<i class="fas fa-language"></i>';
                button.className = 'btn btn-outline-success btn-sm me-2 translate-single-btn';
                button.title = '显示翻译';
            }
        } else {
            button.innerHTML = '<i class="fas fa-language"></i>';
            button.className = 'btn btn-outline-primary btn-sm me-2 translate-single-btn';
            button.title = '翻译';
        }
    }

    showResultDetails(result, position) {
        const detailsContainer = document.getElementById('details-container');

        const detailsHtml = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2 text-primary"></i>
                        因子详情 #${position}
                        <span class="badge bg-gradient bg-primary ms-2">完整BERT</span>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 因子ID -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-tag me-2"></i>
                            因子ID
                        </h6>
                        <div class="bg-light p-3 rounded">
                            <code class="fs-5 text-dark">${result.id}</code>
                        </div>
                    </div>

                    <!-- 因子描述 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-info-circle me-2"></i>
                            因子描述
                        </h6>
                        <div class="bg-light p-3 rounded">
                            ${result.showTranslation && result.description_translated ?
                                `<p class="mb-2"><strong>中文:</strong> ${result.description_translated}</p>
                                 <p class="mb-0 small text-muted"><strong>原文:</strong> ${result.description}</p>` :
                                `<p class="mb-0">${result.description}</p>`
                            }
                        </div>
                    </div>

                    <!-- 分类信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-sitemap me-2"></i>
                            分类信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>数据集:</strong><br>
                                    <span class="text-primary">${result.dataset_name_translated || result.dataset_name}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>主类别:</strong><br>
                                    <span class="text-success">${result.category_name_translated || result.category_name}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>地区:</strong><br>
                                    <span class="text-warning">${result.region || 'N/A'}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <strong>子类别:</strong><br>
                                    <span class="text-info">${result.subcategory_name_translated || result.subcategory_name}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>数据延迟:</strong><br>
                                    <span class="text-secondary">${result.delay || 'N/A'}</span>
                                </div>
                                <div class="mb-2">
                                    <strong>数据范围:</strong><br>
                                    <span class="text-muted">${result.universe || 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 完整BERT评分详情 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-brain me-2"></i>
                            完整BERT评分详情
                        </h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h4>${(result.overall_score * 100).toFixed(1)}%</h4>
                                        <small>综合分数</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h4>${(result.semantic_score * 100).toFixed(1)}%</h4>
                                        <small>语义分数 (70%)</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h4>${(result.keyword_score * 100).toFixed(1)}%</h4>
                                        <small>关键词分数 (30%)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-formula me-1"></i>
                                计算公式: ${(result.semantic_score * 100).toFixed(1)}% × 0.7 + ${(result.keyword_score * 100).toFixed(1)}% × 0.3 = ${(result.overall_score * 100).toFixed(1)}%
                            </small>
                        </div>
                    </div>

                    <!-- 匹配信息 -->
                    <div class="mb-4">
                        <h6 class="text-primary">
                            <i class="fas fa-search me-2"></i>
                            匹配信息
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>匹配字段:</strong><br>
                                ${result.matched_columns.map(col => `<span class="badge bg-secondary me-1">${col}</span>`).join('')}
                            </div>
                            <div class="col-md-6">
                                <strong>匹配关键词:</strong><br>
                                ${result.matched_keywords.slice(0, 5).map(kw => `<span class="badge bg-outline-primary me-1">${kw}</span>`).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        detailsContainer.innerHTML = detailsHtml;
    }

    async adjustThreshold(action) {
        if (!this.currentQuery) {
            this.showError('请先进行搜索');
            return;
        }

        try {
            const response = await fetch('/api/adjust_threshold', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action })
            });

            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data);

                let message = '';
                if (action === 'more_results') {
                    message = `已降低阈值，显示更多结果 (${data.results.length} 条)`;
                } else if (action === 'more_precise') {
                    message = `已提高阈值，显示更精确结果 (${data.results.length} 条)`;
                } else if (action === 'reset') {
                    message = `已重置阈值到默认值 (${data.results.length} 条)`;
                }

                this.showSuccessMessage(message);
            } else {
                this.showError(data.error || '阈值调整失败');
            }

        } catch (error) {
            console.error('Threshold adjustment failed:', error);
            this.showError('阈值调整失败，请重试');
        }
    }

    showLoading(message, progress = 0) {
        const overlay = document.getElementById('loading-overlay');
        const messageEl = document.getElementById('loading-message');
        const progressEl = document.getElementById('loading-progress');

        messageEl.textContent = message;
        progressEl.style.width = `${progress}%`;
        overlay.style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showSuccessMessage(message) {
        // 创建成功消息提示
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 1050; min-width: 300px;" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 3秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-success');
            if (alert) {
                alert.remove();
            }
        }, 3000);
    }

    showError(message) {
        // 创建错误消息提示
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 1050; min-width: 300px;" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // 5秒后自动消失
        setTimeout(() => {
            const alert = document.querySelector('.alert-danger');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

// 初始化应用
const app = new FullBertSearchApp();
