/* BERT融合搜索引擎 - 自定义样式 */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

/* 搜索输入框样式 */
#search-input {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    border-right: none;
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
}

#search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#search-btn {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
}

/* 快速查询按钮 */
.quick-query {
    transition: all 0.2s ease;
    border-radius: 20px;
}

.quick-query:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 阈值调整按钮 */
#more-results-btn, #more-precise-btn, #reset-btn {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
}

#more-results-btn:hover {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: white;
    transform: translateY(-1px);
}

#more-precise-btn:hover {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
    transform: translateY(-1px);
}

/* 阈值状态显示 */
#threshold-status {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
}

#threshold-status .fw-bold {
    font-size: 1.1rem;
    color: #fff;
}

#threshold-status small {
    color: rgba(255, 255, 255, 0.8);
}

/* 加载指示器 */
#loading-indicator {
    padding: 2rem;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 搜索结果样式 */
.result-item {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.result-item:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateX(5px);
}

.result-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 1rem;
}

.result-id {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 1.1rem;
}

.result-score {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.result-body {
    padding: 1.5rem;
}

.result-description {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.result-meta {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 1rem;
}

.meta-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    border-bottom: 1px solid #dee2e6;
}

.meta-item:last-child {
    border-bottom: none;
}

.meta-label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.meta-value {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.9rem;
}

/* 匹配信息 */
.matched-info {
    margin-top: 1rem;
}

.matched-columns {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.column-badge {
    background: var(--info-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.matched-keywords {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-top: 0.5rem;
}

.keyword-badge {
    background: var(--success-color);
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* 搜索统计 */
#search-stats {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
}

#search-stats .fw-bold {
    font-size: 1.1rem;
    color: #fff;
}

#search-stats small {
    color: rgba(255, 255, 255, 0.8);
}

/* 无结果状态 */
.no-results {
    text-align: center;
    padding: 3rem 2rem;
    color: #6c757d;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 错误状态 */
.error-message {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 1rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .result-header {
        padding: 0.75rem;
    }
    
    .result-body {
        padding: 1rem;
    }
    
    .result-id {
        font-size: 1rem;
    }
    
    .matched-columns,
    .matched-keywords {
        justify-content: center;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-ready {
    background-color: var(--success-color);
}

.status-loading {
    background-color: var(--warning-color);
    animation: pulse 1s infinite;
}

.status-error {
    background-color: var(--danger-color);
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    color: white;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

/* 两列布局样式 */
#results-overview {
    max-height: 700px;  /* 增加高度 */
    overflow-y: auto;
}

#result-details {
    max-height: 700px;  /* 增加高度 */
    overflow-y: auto;
}

.result-overview-item {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    padding: 0.5rem 0.75rem;  /* 减少内边距 */
}

.result-overview-item.compact-item {
    padding: 0.4rem 0.75rem;  /* 更紧凑的内边距 */
    min-height: auto;
}

.result-overview-item:hover {
    background-color: #f8f9fa;
    border-left-color: var(--primary-color);
}

.result-overview-item.active {
    background-color: #e3f2fd;
    border-left-color: var(--primary-color);
}

.result-overview-item h6 {
    font-size: 0.95rem;
    font-weight: 600;
}

/* 紧凑列表项样式 */
.compact-item .fw-bold {
    font-size: 0.85rem;
    margin-bottom: 0.1rem;
}

.compact-item .text-muted {
    margin-bottom: 0;
}

.compact-item .badge {
    padding: 0.2em 0.4em;
    font-size: 0.7rem;
}

/* 详情卡片样式 */
.card-header.bg-success,
.card-header.bg-warning,
.card-header.bg-secondary {
    border-bottom: none;
}

.bg-light.p-3.rounded {
    border-left: 4px solid var(--primary-color);
}

/* 徽章样式优化 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

.badge.fs-6 {
    font-size: 0.875rem !important;
    padding: 0.5em 0.75em;
}

/* 响应式优化 */
@media (max-width: 991px) {
    #results-overview,
    #result-details {
        max-height: none;
        margin-bottom: 1rem;
    }

    .result-overview-item {
        margin-bottom: 0.5rem;
        border-radius: var(--border-radius);
        border: 1px solid #dee2e6;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 平滑滚动 */
#results-overview,
#result-details {
    scroll-behavior: smooth;
}

/* 组合因子样式 */
.factor-item {
    transition: all 0.2s ease;
    cursor: pointer;
}

.factor-item:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.factor-item.bg-primary:hover,
.factor-item.bg-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.combination-preview {
    min-height: 200px;
}

.cursor-pointer {
    cursor: pointer;
}

/* 组合因子卡片动画 */
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .display-1 {
        font-size: 2rem;
    }

    .display-4 {
        font-size: 1.5rem;
    }

    .combination-preview {
        min-height: 150px;
    }
}
