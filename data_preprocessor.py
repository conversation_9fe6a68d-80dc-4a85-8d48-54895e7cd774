#!/usr/bin/env python3
"""
数据预处理模块 - 负责数据清洗、索引构建和向量化处理
"""

import pandas as pd
import numpy as np
import jieba
import re
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import pickle
import os
from tqdm import tqdm

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, model_name: str = "paraphrase-multilingual-MiniLM-L12-v2"):
        """
        初始化预处理器

        Args:
            model_name: 用于向量化的模型名称
        """
        self.model_name = model_name
        self.model = None
        self.search_fields = ['id', 'description', 'dataset.id', 'dataset.name',
                             'category.id', 'category.name', 'subcategory.id', 'subcategory.name']
        
    def load_model(self):
        """加载向量化模型"""
        if self.model is None:
            print(f"Loading model: {self.model_name}")
            try:
                self.model = SentenceTransformer(self.model_name)
                print("Model loaded successfully!")
            except Exception as e:
                print(f"Failed to load model {self.model_name}: {e}")
                print("Falling back to basic multilingual model")
                try:
                    self.model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                    print("Fallback model loaded successfully!")
                except Exception as e2:
                    print(f"Failed to load fallback model: {e2}")
                    raise e2
    
    def clean_text(self, text: str) -> str:
        """
        清洗文本数据
        
        Args:
            text: 原始文本
            
        Returns:
            清洗后的文本
        """
        if pd.isna(text) or text is None:
            return ""
        
        text = str(text)
        # 移除多余的空格和换行符
        text = re.sub(r'\s+', ' ', text)
        # 移除特殊字符但保留中文、英文、数字和基本标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-_.,()%/]', ' ', text)
        return text.strip()
    
    def segment_chinese_text(self, text: str) -> List[str]:
        """
        中文分词
        
        Args:
            text: 输入文本
            
        Returns:
            分词结果列表
        """
        if not text:
            return []
        
        # 使用jieba进行中文分词
        words = jieba.lcut(text)
        # 过滤掉长度小于2的词和纯数字
        filtered_words = [word for word in words 
                         if len(word) >= 2 and not word.isdigit() and word.strip()]
        return filtered_words
    
    def create_searchable_text(self, row: pd.Series) -> str:
        """
        创建可搜索的文本内容
        
        Args:
            row: 数据行
            
        Returns:
            合并后的搜索文本
        """
        texts = []
        
        for field in self.search_fields:
            if field in row and pd.notna(row[field]):
                cleaned_text = self.clean_text(row[field])
                if cleaned_text:
                    texts.append(cleaned_text)
        
        return " | ".join(texts)
    
    def load_and_preprocess_data(self, csv_file: str) -> pd.DataFrame:
        """
        加载和预处理数据
        
        Args:
            csv_file: CSV文件路径
            
        Returns:
            预处理后的DataFrame
        """
        print(f"Loading data from {csv_file}...")
        df = pd.read_csv(csv_file)
        
        print(f"Loaded {len(df)} records")
        print("Preprocessing data...")
        
        # 创建搜索文本
        tqdm.pandas(desc="Creating searchable text")
        df['searchable_text'] = df.progress_apply(self.create_searchable_text, axis=1)
        
        # 创建分词字段
        tqdm.pandas(desc="Segmenting text")
        df['segmented_words'] = df['searchable_text'].progress_apply(self.segment_chinese_text)
        
        # 创建关键词字符串（用于传统搜索）
        df['keywords'] = df['segmented_words'].apply(lambda x: ' '.join(x))
        
        return df
    
    def create_embeddings(self, df: pd.DataFrame, batch_size: int = 32) -> np.ndarray:
        """
        创建文本向量
        
        Args:
            df: 预处理后的DataFrame
            batch_size: 批处理大小
            
        Returns:
            向量矩阵
        """
        if self.model is None:
            self.load_model()
        
        print("Creating embeddings...")
        texts = df['searchable_text'].tolist()
        
        # 批量处理以节省内存
        embeddings = []
        for i in tqdm(range(0, len(texts), batch_size), desc="Encoding texts"):
            batch_texts = texts[i:i+batch_size]
            batch_embeddings = self.model.encode(batch_texts, show_progress_bar=False)
            embeddings.append(batch_embeddings)
        
        return np.vstack(embeddings)
    
    def save_processed_data(self, df: pd.DataFrame, embeddings: np.ndarray, 
                           output_dir: str = "processed_data"):
        """
        保存预处理后的数据
        
        Args:
            df: 预处理后的DataFrame
            embeddings: 向量矩阵
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存DataFrame
        df_path = os.path.join(output_dir, "processed_data.pkl")
        df.to_pickle(df_path)
        print(f"Saved processed DataFrame to {df_path}")
        
        # 保存向量
        embeddings_path = os.path.join(output_dir, "embeddings.npy")
        np.save(embeddings_path, embeddings)
        print(f"Saved embeddings to {embeddings_path}")
        
        # 保存元数据
        metadata = {
            'model_name': self.model_name,
            'num_records': len(df),
            'embedding_dim': embeddings.shape[1],
            'search_fields': self.search_fields
        }
        
        metadata_path = os.path.join(output_dir, "metadata.pkl")
        with open(metadata_path, 'wb') as f:
            pickle.dump(metadata, f)
        print(f"Saved metadata to {metadata_path}")
    
    def process_data(self, csv_file: str, output_dir: str = "processed_data"):
        """
        完整的数据处理流程
        
        Args:
            csv_file: 输入CSV文件
            output_dir: 输出目录
        """
        # 加载和预处理数据
        df = self.load_and_preprocess_data(csv_file)
        
        # 创建向量
        embeddings = self.create_embeddings(df)
        
        # 保存结果
        self.save_processed_data(df, embeddings, output_dir)
        
        print("Data processing completed!")
        return df, embeddings

if __name__ == "__main__":
    # 示例用法
    preprocessor = DataPreprocessor()
    
    # 处理原始数据
    df, embeddings = preprocessor.process_data("data.csv")
    
    print(f"Processed {len(df)} records")
    print(f"Embedding shape: {embeddings.shape}")
