#!/usr/bin/env python3
"""
Script to split data.csv into multiple files based on region_delay_universe combinations
"""

import pandas as pd
import os
from collections import defaultdict
import re

def clean_filename(text):
    """Clean text to make it suitable for filename"""
    # Replace problematic characters with underscores
    text = re.sub(r'[^\w\-_.]', '_', text)
    # Remove multiple consecutive underscores
    text = re.sub(r'_+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    return text

def split_csv_by_region_delay_universe(input_file, output_dir='split_files'):
    """
    Split the CSV file based on region_delay_universe combinations
    """
    print(f"Reading {input_file}...")
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Read the CSV file in chunks to handle large files efficiently
    chunk_size = 10000
    file_handles = {}
    file_writers = {}
    header_written = {}
    
    try:
        for chunk_num, chunk in enumerate(pd.read_csv(input_file, chunksize=chunk_size)):
            print(f"Processing chunk {chunk_num + 1}...")
            
            # Group by region_delay_universe combination
            for (region, delay, universe), group in chunk.groupby(['region', 'delay', 'universe']):
                # Create filename
                filename_parts = [
                    clean_filename(str(region)),
                    clean_filename(str(delay)),
                    clean_filename(str(universe))
                ]
                filename = '_'.join(filename_parts) + '.csv'
                filepath = os.path.join(output_dir, filename)
                
                # Open file handle if not already open
                if filepath not in file_handles:
                    file_handles[filepath] = open(filepath, 'w', encoding='utf-8')
                    file_writers[filepath] = None
                    header_written[filepath] = False
                
                # Write data to file
                if not header_written[filepath]:
                    # Write with header for the first time
                    group.to_csv(file_handles[filepath], index=False, mode='w')
                    header_written[filepath] = True
                else:
                    # Append without header
                    group.to_csv(file_handles[filepath], index=False, mode='a', header=False)
    
    finally:
        # Close all file handles
        for handle in file_handles.values():
            handle.close()
    
    print(f"Split complete! Files saved in '{output_dir}' directory")
    print(f"Total files created: {len(file_handles)}")
    
    # Print summary of files created
    print("\nFiles created:")
    for filepath in sorted(file_handles.keys()):
        filename = os.path.basename(filepath)
        try:
            # Count lines in the file (excluding header)
            with open(filepath, 'r') as f:
                line_count = sum(1 for line in f) - 1  # Subtract 1 for header
            print(f"  {filename}: {line_count} records")
        except:
            print(f"  {filename}: Error reading file")

if __name__ == "__main__":
    input_file = "data.csv"
    output_dir = "split_files"
    
    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found!")
        exit(1)
    
    split_csv_by_region_delay_universe(input_file, output_dir)
