#!/usr/bin/env python3
"""
测试翻译功能
"""

import pandas as pd

# 测试翻译功能
def test_translation():
    print("🧪 Testing translation functionality...")
    
    try:
        from translation_dict import (
            translate_factor_id, translate_description, translate_text,
            DATASET_TRANSLATIONS, CATEGORY_TRANSLATIONS
        )
        
        # 测试因子ID翻译
        test_factors = [
            "act_12m_eps_value",
            "est_q_roe_mean", 
            "analyst_rating_consensus"
        ]
        
        print("因子ID翻译测试:")
        for factor in test_factors:
            translated = translate_factor_id(factor)
            print(f"   {factor} → {translated}")
        
        # 测试数据集翻译
        print("\n数据集翻译测试:")
        for en, cn in list(DATASET_TRANSLATIONS.items())[:3]:
            print(f"   {en} → {cn}")
        
        print("✅ Translation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        return False

if __name__ == "__main__":
    test_translation()
