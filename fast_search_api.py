#!/usr/bin/env python3
"""
基于FastAPI的高性能搜索API服务
支持毫秒级响应和并发处理
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
import uvicorn
from loguru import logger
import pandas as pd
from pathlib import Path
import json

# 导入我们的搜索引擎
from advanced_search_engine import AdvancedSearchEngine, SearchMode, SearchResult
from enhanced_chinese_processor import EnhancedChineseProcessor

# 数据模型
class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索查询", min_length=1, max_length=200)
    mode: str = Field("hybrid", description="搜索模式: semantic, keyword, hybrid")
    top_k: int = Field(10, description="返回结果数量", ge=1, le=100)
    region: Optional[str] = Field(None, description="地区过滤")
    universe: Optional[str] = Field(None, description="范围过滤")
    delay: Optional[int] = Field(None, description="延迟过滤")

class SearchResponse(BaseModel):
    """搜索响应模型"""
    query: str
    mode: str
    results: List[Dict[str, Any]]
    total_results: int
    search_time: float
    query_analysis: Optional[Dict[str, Any]] = None

class IndexRequest(BaseModel):
    """索引构建请求"""
    region: str
    universe: str
    delay: int
    rebuild: bool = False

# 全局变量
search_engine: Optional[AdvancedSearchEngine] = None
text_processor: Optional[EnhancedChineseProcessor] = None
current_data_info: Dict[str, Any] = {}

# FastAPI应用
app = FastAPI(
    title="高性能中文搜索API",
    description="基于深度学习的毫秒级中文搜索服务",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global search_engine, text_processor
    
    logger.info("🚀 Starting Advanced Search API...")
    
    # 初始化文本处理器
    text_processor = EnhancedChineseProcessor()
    logger.success("✅ Text processor initialized")
    
    # 初始化搜索引擎
    search_engine = AdvancedSearchEngine()
    logger.success("✅ Search engine initialized")
    
    logger.info("🎉 API server ready!")

@app.get("/", response_class=HTMLResponse)
async def root():
    """主页 - 搜索界面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>高性能中文搜索系统 v2.0</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; color: white; margin-bottom: 40px; }
            .header h1 { font-size: 3em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .header p { font-size: 1.2em; opacity: 0.9; }
            .search-card { background: white; border-radius: 20px; padding: 40px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); }
            .search-form { margin-bottom: 30px; }
            .search-input { width: 100%; padding: 20px; font-size: 18px; border: 2px solid #e1e5e9; 
                           border-radius: 15px; outline: none; transition: all 0.3s; }
            .search-input:focus { border-color: #667eea; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }
            .search-controls { display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap; }
            .control-group { display: flex; align-items: center; gap: 10px; }
            .control-group label { font-weight: 600; color: #4a5568; }
            .control-group select, .control-group input { padding: 10px; border: 1px solid #e1e5e9; 
                                                         border-radius: 8px; outline: none; }
            .search-button { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                           color: white; border: none; padding: 20px 40px; font-size: 18px; 
                           border-radius: 15px; cursor: pointer; transition: all 0.3s; }
            .search-button:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3); }
            .results { margin-top: 30px; }
            .result-item { background: #f8f9fa; border-radius: 15px; padding: 25px; margin: 15px 0; 
                          border-left: 5px solid #667eea; transition: all 0.3s; }
            .result-item:hover { transform: translateX(5px); box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
            .result-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
            .result-title { font-size: 1.2em; font-weight: 600; color: #2d3748; }
            .result-score { background: #667eea; color: white; padding: 5px 10px; border-radius: 20px; font-size: 0.9em; }
            .result-description { color: #4a5568; margin: 10px 0; line-height: 1.6; }
            .result-meta { font-size: 0.9em; color: #718096; }
            .loading { text-align: center; padding: 40px; color: #667eea; }
            .stats { background: #e6fffa; border-radius: 10px; padding: 20px; margin: 20px 0; }
            .error { background: #fed7d7; color: #c53030; padding: 20px; border-radius: 10px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔍 高性能中文搜索</h1>
                <p>基于深度学习的毫秒级搜索体验</p>
            </div>
            
            <div class="search-card">
                <div class="search-form">
                    <input type="text" id="searchQuery" class="search-input" 
                           placeholder="输入搜索关键词，如：每股收益、EBITDA、分析师预测..." 
                           autocomplete="off">
                    
                    <div class="search-controls">
                        <div class="control-group">
                            <label>搜索模式:</label>
                            <select id="searchMode">
                                <option value="hybrid">混合搜索 (推荐)</option>
                                <option value="semantic">语义搜索</option>
                                <option value="keyword">关键词搜索</option>
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label>结果数量:</label>
                            <select id="topK">
                                <option value="5">5</option>
                                <option value="10" selected>10</option>
                                <option value="20">20</option>
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label>地区:</label>
                            <select id="region">
                                <option value="">全部</option>
                                <option value="USA">美国</option>
                                <option value="EUR">欧洲</option>
                                <option value="CHN">中国</option>
                                <option value="GLB">全球</option>
                            </select>
                        </div>
                    </div>
                    
                    <button onclick="performSearch()" class="search-button">🚀 开始搜索</button>
                </div>
                
                <div id="results" class="results"></div>
            </div>
        </div>
        
        <script>
            let searchTimeout;
            
            // 实时搜索
            document.getElementById('searchQuery').addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => performSearch(), 500);
                }
            });
            
            // 回车搜索
            document.getElementById('searchQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
            
            async function performSearch() {
                const query = document.getElementById('searchQuery').value.trim();
                const mode = document.getElementById('searchMode').value;
                const topK = document.getElementById('topK').value;
                const region = document.getElementById('region').value;
                const resultsDiv = document.getElementById('results');
                
                if (!query) {
                    resultsDiv.innerHTML = '';
                    return;
                }
                
                resultsDiv.innerHTML = '<div class="loading">🔍 搜索中...</div>';
                
                try {
                    const params = new URLSearchParams({
                        query: query,
                        mode: mode,
                        top_k: topK
                    });
                    
                    if (region) params.append('region', region);
                    
                    const response = await fetch(`/api/search?${params}`);
                    const data = await response.json();
                    
                    if (data.error) {
                        resultsDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                        return;
                    }
                    
                    displayResults(data);
                    
                } catch (error) {
                    resultsDiv.innerHTML = `<div class="error">❌ 搜索失败: ${error.message}</div>`;
                }
            }
            
            function displayResults(data) {
                const resultsDiv = document.getElementById('results');
                
                if (!data.results || data.results.length === 0) {
                    resultsDiv.innerHTML = '<div class="error">😔 未找到相关结果</div>';
                    return;
                }
                
                let html = `
                    <div class="stats">
                        📊 找到 <strong>${data.total_results}</strong> 个结果，
                        耗时 <strong>${(data.search_time * 1000).toFixed(1)}ms</strong>，
                        搜索模式: <strong>${data.mode}</strong>
                    </div>
                `;
                
                data.results.forEach((result, index) => {
                    html += `
                        <div class="result-item">
                            <div class="result-header">
                                <div class="result-title">${index + 1}. ${result.id}</div>
                                <div class="result-score">${result.score.toFixed(4)}</div>
                            </div>
                            <div class="result-description">${result.description}</div>
                            <div class="result-meta">
                                📁 数据集: ${result.dataset_name} | 
                                📂 类别: ${result.category_name} | 
                                🏷️ 子类别: ${result.subcategory_name}
                            </div>
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = html;
            }
            
            // 页面加载完成后的初始化
            document.addEventListener('DOMContentLoaded', function() {
                // 检查API状态
                fetch('/api/health')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status !== 'ready') {
                            document.getElementById('results').innerHTML = 
                                '<div class="error">⚠️ 搜索引擎未就绪，请稍后再试</div>';
                        }
                    })
                    .catch(error => {
                        console.warn('Health check failed:', error);
                    });
            });
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/api/search", response_model=SearchResponse)
async def search_api(request: SearchRequest):
    """搜索API接口"""
    if search_engine is None:
        raise HTTPException(status_code=503, detail="搜索引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 构建过滤条件
        filters = {}
        if request.region:
            filters["region"] = request.region
        if request.universe:
            filters["universe"] = request.universe
        if request.delay is not None:
            filters["delay"] = str(request.delay)
        
        # 解析搜索模式
        try:
            mode = SearchMode(request.mode)
        except ValueError:
            mode = SearchMode.HYBRID
        
        # 执行搜索
        results = search_engine.search(
            query=request.query,
            mode=mode,
            top_k=request.top_k,
            filters=filters if filters else None
        )
        
        # 查询分析
        query_analysis = None
        if text_processor:
            query_analysis = text_processor.analyze_query_intent(request.query)
        
        search_time = time.time() - start_time
        
        # 转换结果格式
        result_dicts = []
        for result in results:
            result_dicts.append({
                "id": result.id,
                "description": result.description,
                "dataset_name": result.dataset_name,
                "category_name": result.category_name,
                "subcategory_name": result.subcategory_name,
                "score": result.score,
                "search_mode": result.search_mode,
                "metadata": result.metadata
            })
        
        return SearchResponse(
            query=request.query,
            mode=request.mode,
            results=result_dicts,
            total_results=len(results),
            search_time=search_time,
            query_analysis=query_analysis
        )
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@app.get("/api/search")
async def search_get(
    query: str = Query(..., description="搜索查询"),
    mode: str = Query("hybrid", description="搜索模式"),
    top_k: int = Query(10, description="返回结果数量"),
    region: Optional[str] = Query(None, description="地区过滤"),
    universe: Optional[str] = Query(None, description="范围过滤"),
    delay: Optional[int] = Query(None, description="延迟过滤")
):
    """GET方式的搜索接口"""
    request = SearchRequest(
        query=query,
        mode=mode,
        top_k=top_k,
        region=region,
        universe=universe,
        delay=delay
    )
    return await search_api(request)

@app.post("/api/build_index")
async def build_index(request: IndexRequest, background_tasks: BackgroundTasks):
    """构建索引接口"""
    if search_engine is None:
        raise HTTPException(status_code=503, detail="搜索引擎未初始化")
    
    # 在后台任务中构建索引
    background_tasks.add_task(
        _build_index_task,
        request.region,
        request.universe,
        request.delay,
        request.rebuild
    )
    
    return {"message": "索引构建任务已启动", "status": "building"}

async def _build_index_task(region: str, universe: str, delay: int, rebuild: bool):
    """后台索引构建任务"""
    global current_data_info
    
    try:
        logger.info(f"Building index for {region}_{universe}_{delay}")
        
        # 加载对应的分割数据
        file_path = f"split_files/{region}_{delay}_{universe}.csv"
        if not Path(file_path).exists():
            logger.error(f"Data file not found: {file_path}")
            return
        
        df = pd.read_csv(file_path)
        logger.info(f"Loaded {len(df)} records from {file_path}")
        
        # 构建索引
        search_engine.build_index(df)
        
        # 更新当前数据信息
        current_data_info = {
            "region": region,
            "universe": universe,
            "delay": delay,
            "total_records": len(df),
            "build_time": time.time()
        }
        
        logger.success(f"Index built successfully for {region}_{universe}_{delay}")
        
    except Exception as e:
        logger.error(f"Index building failed: {e}")

@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    if search_engine is None:
        return {"status": "not_ready", "message": "搜索引擎未初始化"}
    
    stats = search_engine.get_stats()
    
    return {
        "status": "ready" if stats.get("total_documents", 0) > 0 else "no_data",
        "engine_stats": stats,
        "current_data": current_data_info,
        "text_processor": "ready" if text_processor else "not_ready"
    }

@app.get("/api/stats")
async def get_stats():
    """获取统计信息"""
    if search_engine is None:
        return {"error": "搜索引擎未初始化"}
    
    return {
        "engine_stats": search_engine.get_stats(),
        "current_data": current_data_info,
        "available_files": list(Path("split_files").glob("*.csv")) if Path("split_files").exists() else []
    }

if __name__ == "__main__":
    uvicorn.run(
        "fast_search_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
