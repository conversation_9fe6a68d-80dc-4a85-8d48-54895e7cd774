#!/usr/bin/env python3
"""
中英文翻译字典
将所有英文内容翻译成中文，包括因子信息和界面文本
"""

# 数据集名称翻译
DATASET_TRANSLATIONS = {
    'Broker Estimates': '券商预测',
    'Fundamental Analysis': '基本面分析',
    'Technical Analysis': '技术分析',
    'Market Data': '市场数据',
    'Financial Statements': '财务报表',
    'Economic Indicators': '经济指标',
    'Company Information': '公司信息',
    'Industry Analysis': '行业分析',
    'Risk Metrics': '风险指标',
    'Valuation Metrics': '估值指标',
    'Growth Metrics': '成长指标',
    'Profitability Metrics': '盈利能力指标',
    'Liquidity Metrics': '流动性指标',
    'Efficiency Metrics': '效率指标',
    'Leverage Metrics': '杠杆指标',
    'Quality Metrics': '质量指标',
    'Momentum Metrics': '动量指标',
    'Sentiment Metrics': '情绪指标'
}

# 类别名称翻译
CATEGORY_TRANSLATIONS = {
    'Analyst': '分析师',
    'Fundamental': '基本面',
    'Technical': '技术面',
    'Market': '市场',
    'Financial': '财务',
    'Economic': '经济',
    'Company': '公司',
    'Industry': '行业',
    'Risk': '风险',
    'Valuation': '估值',
    'Growth': '成长',
    'Profitability': '盈利能力',
    'Liquidity': '流动性',
    'Efficiency': '效率',
    'Leverage': '杠杆',
    'Quality': '质量',
    'Momentum': '动量',
    'Sentiment': '情绪',
    'ESG': '环境社会治理'
}

# 子类别名称翻译
SUBCATEGORY_TRANSLATIONS = {
    'Analyst Estimates': '分析师预测',
    'Analyst Recommendations': '分析师建议',
    'Earnings Estimates': '盈利预测',
    'Revenue Estimates': '营收预测',
    'Price Targets': '目标价',
    'Rating Changes': '评级变化',
    'Consensus Estimates': '一致预期',
    'Estimate Revisions': '预测修正',
    'Surprise History': '超预期历史',
    'Forward Looking': '前瞻性',
    'Historical Data': '历史数据',
    'Current Metrics': '当前指标',
    'Trailing Metrics': '滚动指标',
    'Forward Metrics': '前瞻指标',
    'Quarterly Data': '季度数据',
    'Annual Data': '年度数据',
    'Monthly Data': '月度数据',
    'Daily Data': '日度数据',
    'Real Time': '实时数据',
    'End of Day': '收盘数据'
}

# 因子ID翻译（常见的金融因子）
FACTOR_ID_TRANSLATIONS = {
    # 盈利能力相关
    'eps': '每股收益',
    'roe': '净资产收益率',
    'roa': '总资产收益率',
    'roic': '投入资本回报率',
    'gross_margin': '毛利率',
    'operating_margin': '营业利润率',
    'net_margin': '净利率',
    'ebitda_margin': 'EBITDA利润率',
    
    # 估值相关
    'pe': '市盈率',
    'pb': '市净率',
    'ps': '市销率',
    'pcf': '市现率',
    'ev_ebitda': '企业价值倍数',
    'peg': 'PEG比率',
    
    # 成长性相关
    'revenue_growth': '营收增长率',
    'earnings_growth': '盈利增长率',
    'eps_growth': '每股收益增长率',
    'book_value_growth': '净资产增长率',
    'dividend_growth': '股息增长率',
    
    # 财务健康度
    'debt_to_equity': '资产负债率',
    'current_ratio': '流动比率',
    'quick_ratio': '速动比率',
    'cash_ratio': '现金比率',
    'interest_coverage': '利息保障倍数',
    
    # 效率指标
    'asset_turnover': '资产周转率',
    'inventory_turnover': '存货周转率',
    'receivables_turnover': '应收账款周转率',
    'working_capital_turnover': '营运资金周转率',
    
    # 分析师相关
    'analyst_rating': '分析师评级',
    'target_price': '目标价',
    'price_target_upside': '目标价上涨空间',
    'consensus_rating': '一致评级',
    'rating_change': '评级变化',
    'estimate_revision': '预测修正',
    'earnings_surprise': '盈利超预期',
    'revenue_surprise': '营收超预期',
    
    # 技术指标
    'rsi': '相对强弱指数',
    'macd': 'MACD',
    'bollinger_bands': '布林带',
    'moving_average': '移动平均线',
    'volume': '成交量',
    'volatility': '波动率',
    'beta': '贝塔系数',
    'alpha': '阿尔法系数',
    
    # 市场指标
    'market_cap': '市值',
    'enterprise_value': '企业价值',
    'shares_outstanding': '流通股本',
    'float_shares': '自由流通股',
    'institutional_ownership': '机构持股比例',
    'insider_ownership': '内部人持股比例',
    
    # 股息相关
    'dividend_yield': '股息率',
    'dividend_payout_ratio': '股息支付率',
    'dividend_per_share': '每股股息',
    'ex_dividend_date': '除息日',
    
    # ESG相关
    'esg_score': 'ESG评分',
    'environmental_score': '环境评分',
    'social_score': '社会评分',
    'governance_score': '治理评分',
    'carbon_footprint': '碳足迹',
    'sustainability_rating': '可持续性评级'
}

# 描述文本翻译模式
DESCRIPTION_PATTERNS = {
    # 时间相关
    r'(\d+)\s*year': r'\1年',
    r'(\d+)\s*month': r'\1个月',
    r'(\d+)\s*quarter': r'\1季度',
    r'(\d+)\s*week': r'\1周',
    r'(\d+)\s*day': r'\1天',
    
    # 常用词汇
    'actual': '实际',
    'estimate': '预测',
    'forecast': '预测',
    'consensus': '一致',
    'median': '中位数',
    'mean': '平均',
    'high': '最高',
    'low': '最低',
    'current': '当前',
    'previous': '前期',
    'next': '下期',
    'trailing': '滚动',
    'forward': '前瞻',
    'growth': '增长',
    'change': '变化',
    'ratio': '比率',
    'margin': '利润率',
    'return': '回报',
    'yield': '收益率',
    'value': '价值',
    'price': '价格',
    'earnings': '盈利',
    'revenue': '营收',
    'sales': '销售',
    'profit': '利润',
    'income': '收入',
    'cash': '现金',
    'debt': '债务',
    'equity': '权益',
    'assets': '资产',
    'liabilities': '负债',
    'shares': '股份',
    'dividend': '股息',
    'buyback': '回购',
    'split': '拆股',
    'merger': '并购',
    'acquisition': '收购',
    'spinoff': '分拆',
    'ipo': '首次公开发行',
    'delisting': '退市',
    'bankruptcy': '破产',
    'restructuring': '重组'
}

# 地区翻译
REGION_TRANSLATIONS = {
    'USA': '美国',
    'China': '中国',
    'Japan': '日本',
    'Europe': '欧洲',
    'UK': '英国',
    'Germany': '德国',
    'France': '法国',
    'Canada': '加拿大',
    'Australia': '澳大利亚',
    'South Korea': '韩国',
    'India': '印度',
    'Brazil': '巴西',
    'Russia': '俄罗斯',
    'Global': '全球',
    'Developed Markets': '发达市场',
    'Emerging Markets': '新兴市场',
    'Asia Pacific': '亚太地区',
    'North America': '北美',
    'Latin America': '拉丁美洲',
    'Middle East': '中东',
    'Africa': '非洲'
}

# 延迟类型翻译
DELAY_TRANSLATIONS = {
    'Real Time': '实时',
    'End of Day': '收盘后',
    '15 min': '15分钟延迟',
    '20 min': '20分钟延迟',
    'Daily': '日更新',
    'Weekly': '周更新',
    'Monthly': '月更新',
    'Quarterly': '季度更新',
    'Annual': '年度更新',
    'Intraday': '盘中',
    'After Hours': '盘后',
    'Pre Market': '盘前'
}

def translate_text(text: str, translation_dict: dict) -> str:
    """翻译文本"""
    if not text:
        return text

    # 检查是否为NaN（如果pandas可用）
    try:
        import pandas as pd
        if pd.isna(text):
            return text
    except ImportError:
        pass
    
    text_str = str(text)
    
    # 直接匹配
    if text_str in translation_dict:
        return translation_dict[text_str]
    
    # 部分匹配
    for en_text, cn_text in translation_dict.items():
        if en_text.lower() in text_str.lower():
            text_str = text_str.replace(en_text, cn_text)
    
    return text_str

def translate_factor_id(factor_id: str) -> str:
    """翻译因子ID"""
    if not factor_id:
        return factor_id
    
    factor_lower = factor_id.lower()
    
    # 直接匹配
    for en_id, cn_name in FACTOR_ID_TRANSLATIONS.items():
        if en_id in factor_lower:
            return cn_name
    
    # 模式匹配
    import re
    
    # EPS相关
    if 'eps' in factor_lower:
        if 'growth' in factor_lower:
            return '每股收益增长率'
        elif 'estimate' in factor_lower:
            return '每股收益预测'
        elif 'actual' in factor_lower:
            return '每股收益实际值'
        else:
            return '每股收益'
    
    # ROE相关
    if 'roe' in factor_lower or 'return_on_equity' in factor_lower:
        return '净资产收益率'
    
    # 分析师相关
    if 'analyst' in factor_lower:
        if 'rating' in factor_lower:
            return '分析师评级'
        elif 'target' in factor_lower:
            return '分析师目标价'
        elif 'estimate' in factor_lower:
            return '分析师预测'
        else:
            return '分析师观点'
    
    # 如果没有匹配，返回原文
    return factor_id

def translate_description(description: str) -> str:
    """翻译描述文本"""
    if not description:
        return description
    
    import re
    
    translated = str(description)
    
    # 应用模式翻译
    for pattern, replacement in DESCRIPTION_PATTERNS.items():
        if isinstance(pattern, str):
            translated = translated.replace(pattern, replacement)
        else:
            translated = re.sub(pattern, replacement, translated)
    
    return translated

def get_all_translations() -> dict:
    """获取所有翻译字典"""
    return {
        'datasets': DATASET_TRANSLATIONS,
        'categories': CATEGORY_TRANSLATIONS,
        'subcategories': SUBCATEGORY_TRANSLATIONS,
        'factors': FACTOR_ID_TRANSLATIONS,
        'regions': REGION_TRANSLATIONS,
        'delays': DELAY_TRANSLATIONS,
        'patterns': DESCRIPTION_PATTERNS
    }

if __name__ == "__main__":
    # 测试翻译功能
    print("🧪 Testing Translation Functions")
    print("=" * 50)
    
    # 测试因子ID翻译
    test_factors = [
        "act_12m_eps_value",
        "est_q_roe_mean", 
        "analyst_rating_consensus",
        "pv87_ebitda_actual"
    ]
    
    print("因子ID翻译测试:")
    for factor in test_factors:
        translated = translate_factor_id(factor)
        print(f"   {factor} → {translated}")
    
    # 测试描述翻译
    test_descriptions = [
        "12 month trailing earnings per share",
        "Analyst consensus rating for next quarter",
        "Forward looking revenue growth estimate"
    ]
    
    print("\n描述翻译测试:")
    for desc in test_descriptions:
        translated = translate_description(desc)
        print(f"   {desc} → {translated}")
    
    print("\n🎉 Translation test completed!")
