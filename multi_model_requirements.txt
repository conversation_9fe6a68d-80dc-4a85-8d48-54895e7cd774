# 多模型融合搜索系统依赖包

# 核心深度学习框架
torch>=2.0.0
transformers>=4.35.0
sentence-transformers>=2.2.2

# 中文专业模型
chinese-bert-wwm>=1.0.0
bert4keras>=0.11.5

# 预训练模型库
huggingface-hub>=0.17.0
tokenizers>=0.14.0

# 特征融合和向量处理
scikit-learn>=1.3.0
numpy>=1.24.0
pandas>=2.0.0
faiss-cpu>=1.7.4
chromadb>=0.4.15

# 中文NLP工具
jieba>=0.42.1
pkuseg>=0.0.25
LAC>=2.1.2
paddlenlp>=2.6.0

# 实体识别和语义分析
spacy>=3.7.0
zh-core-web-sm>=3.7.0
hanlp>=2.1.0

# 文本相似度和匹配
fuzzywuzzy>=0.18.0
python-levenshtein>=0.21.0
difflib

# 数据处理和可视化
tqdm>=4.66.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Web框架和API
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.4.0

# 缓存和性能优化
redis>=5.0.0
diskcache>=5.6.0
joblib>=1.3.0

# 配置和日志
pyyaml>=6.0.1
loguru>=0.7.2
python-dotenv>=1.0.0

# 监控和调试
psutil>=5.9.0
memory-profiler>=0.61.0
