#!/usr/bin/env python3
"""
动态阈值管理器
根据查询类型、结果质量、用户反馈等因素智能调整检索阈值
"""

import time
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import pickle
from pathlib import Path

class QueryType(Enum):
    """查询类型"""
    EXACT_MATCH = "exact_match"          # 精确匹配 (ID查询等)
    SEMANTIC_SEARCH = "semantic_search"   # 语义搜索 (描述性查询)
    KEYWORD_SEARCH = "keyword_search"     # 关键词搜索 (术语查询)
    MIXED_LANGUAGE = "mixed_language"     # 混合语言查询
    FINANCIAL_TERM = "financial_term"     # 金融术语查询
    ANALYST_QUERY = "analyst_query"       # 分析师相关查询
    COMPLEX_QUERY = "complex_query"       # 复杂查询

class SearchContext(Enum):
    """搜索上下文"""
    INTERACTIVE = "interactive"           # 交互式搜索
    BATCH_PROCESSING = "batch"           # 批量处理
    API_CALL = "api"                     # API调用
    REAL_TIME = "real_time"              # 实时搜索

@dataclass
class ThresholdConfig:
    """阈值配置"""
    # 基础阈值
    min_score_threshold: float = 0.01    # 最低分数阈值
    semantic_threshold: float = 0.3      # 语义相似度阈值
    keyword_threshold: float = 0.2       # 关键词匹配阈值
    
    # 结果数量控制
    min_results: int = 1                 # 最少结果数
    max_results: int = 100               # 最多结果数
    target_results: int = 10             # 目标结果数
    
    # 质量控制
    quality_threshold: float = 0.5       # 结果质量阈值
    diversity_threshold: float = 0.8     # 结果多样性阈值
    
    # 动态调整参数
    adjustment_rate: float = 0.1         # 调整速率
    learning_rate: float = 0.05          # 学习速率
    decay_factor: float = 0.95           # 衰减因子

@dataclass
class SearchMetrics:
    """搜索指标"""
    query: str
    query_type: QueryType
    search_context: SearchContext
    
    # 结果指标
    total_results: int
    avg_score: float
    max_score: float
    min_score: float
    
    # 性能指标
    search_time: float
    user_satisfaction: Optional[float] = None
    click_through_rate: Optional[float] = None
    
    # 阈值信息
    applied_thresholds: Dict[str, float] = None
    
    # 时间戳
    timestamp: float = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()

class DynamicThresholdManager:
    """动态阈值管理器"""
    
    def __init__(self, config_file: str = "threshold_config.json"):
        self.config_file = config_file
        self.base_config = ThresholdConfig()
        
        # 不同查询类型的阈值配置
        self.query_type_configs = self._initialize_query_type_configs()
        
        # 历史数据存储
        self.search_history = deque(maxlen=10000)  # 保留最近10000次搜索
        self.performance_metrics = defaultdict(list)
        
        # 学习参数
        self.threshold_adjustments = defaultdict(float)
        self.success_rates = defaultdict(list)
        
        # 加载配置
        self.load_config()
    
    def _initialize_query_type_configs(self) -> Dict[QueryType, ThresholdConfig]:
        """初始化不同查询类型的阈值配置"""
        configs = {}
        
        # 精确匹配查询 - 高阈值，追求精确性
        configs[QueryType.EXACT_MATCH] = ThresholdConfig(
            min_score_threshold=0.1,
            semantic_threshold=0.6,
            keyword_threshold=0.8,
            target_results=5,
            quality_threshold=0.8
        )
        
        # 语义搜索 - 中等阈值，平衡相关性和召回率
        configs[QueryType.SEMANTIC_SEARCH] = ThresholdConfig(
            min_score_threshold=0.05,
            semantic_threshold=0.4,
            keyword_threshold=0.3,
            target_results=10,
            quality_threshold=0.6
        )
        
        # 关键词搜索 - 低语义阈值，高关键词阈值
        configs[QueryType.KEYWORD_SEARCH] = ThresholdConfig(
            min_score_threshold=0.02,
            semantic_threshold=0.2,
            keyword_threshold=0.6,
            target_results=15,
            quality_threshold=0.5
        )
        
        # 混合语言查询 - 降低阈值以提高召回率
        configs[QueryType.MIXED_LANGUAGE] = ThresholdConfig(
            min_score_threshold=0.01,
            semantic_threshold=0.3,
            keyword_threshold=0.4,
            target_results=12,
            quality_threshold=0.4
        )
        
        # 金融术语查询 - 高精度要求
        configs[QueryType.FINANCIAL_TERM] = ThresholdConfig(
            min_score_threshold=0.08,
            semantic_threshold=0.5,
            keyword_threshold=0.7,
            target_results=8,
            quality_threshold=0.7
        )
        
        # 分析师查询 - 平衡精度和召回率
        configs[QueryType.ANALYST_QUERY] = ThresholdConfig(
            min_score_threshold=0.03,
            semantic_threshold=0.4,
            keyword_threshold=0.5,
            target_results=10,
            quality_threshold=0.6
        )
        
        # 复杂查询 - 低阈值，高容错性
        configs[QueryType.COMPLEX_QUERY] = ThresholdConfig(
            min_score_threshold=0.01,
            semantic_threshold=0.25,
            keyword_threshold=0.3,
            target_results=20,
            quality_threshold=0.4
        )
        
        return configs
    
    def classify_query_type(self, query: str, search_results: List[Any] = None) -> QueryType:
        """分类查询类型"""
        query_lower = query.lower()
        
        # 精确匹配查询 (ID模式)
        if (len(query.split()) <= 2 and 
            ('_' in query or query.isupper() or 
             any(char.isdigit() for char in query))):
            return QueryType.EXACT_MATCH
        
        # 金融术语查询
        financial_terms = [
            'eps', 'roe', 'roa', 'ebitda', 'pe', 'pb', 'ps',
            '每股收益', '净资产收益率', '市盈率', '现金流', '营业收入'
        ]
        if any(term in query_lower for term in financial_terms):
            return QueryType.FINANCIAL_TERM
        
        # 分析师查询
        analyst_terms = ['分析师', '预测', '评级', '目标价', 'analyst', 'forecast', 'rating']
        if any(term in query_lower for term in analyst_terms):
            return QueryType.ANALYST_QUERY
        
        # 混合语言查询
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in query)
        has_english = any(char.isalpha() and ord(char) < 128 for char in query)
        if has_chinese and has_english:
            return QueryType.MIXED_LANGUAGE
        
        # 复杂查询 (长句子)
        if len(query.split()) > 6:
            return QueryType.COMPLEX_QUERY
        
        # 关键词搜索 (短词汇)
        if len(query.split()) <= 3:
            return QueryType.KEYWORD_SEARCH
        
        # 默认语义搜索
        return QueryType.SEMANTIC_SEARCH
    
    def get_adaptive_thresholds(self, query: str, 
                               search_context: SearchContext = SearchContext.INTERACTIVE,
                               user_preferences: Dict[str, Any] = None) -> ThresholdConfig:
        """获取自适应阈值"""
        # 分类查询类型
        query_type = self.classify_query_type(query)
        
        # 获取基础配置
        base_config = self.query_type_configs.get(query_type, self.base_config)
        
        # 创建自适应配置
        adaptive_config = ThresholdConfig(**asdict(base_config))
        
        # 根据搜索上下文调整
        if search_context == SearchContext.REAL_TIME:
            # 实时搜索需要更快响应，降低阈值
            adaptive_config.min_score_threshold *= 0.8
            adaptive_config.target_results = min(adaptive_config.target_results, 5)
        elif search_context == SearchContext.BATCH_PROCESSING:
            # 批量处理可以提高阈值，追求质量
            adaptive_config.quality_threshold *= 1.2
            adaptive_config.min_score_threshold *= 1.1
        
        # 根据历史性能调整
        historical_adjustment = self._get_historical_adjustment(query_type)
        adaptive_config.min_score_threshold += historical_adjustment
        adaptive_config.semantic_threshold += historical_adjustment * 0.5
        adaptive_config.keyword_threshold += historical_adjustment * 0.3
        
        # 用户偏好调整
        if user_preferences:
            if user_preferences.get('prefer_precision', False):
                adaptive_config.quality_threshold *= 1.3
                adaptive_config.min_score_threshold *= 1.2
            elif user_preferences.get('prefer_recall', False):
                adaptive_config.min_score_threshold *= 0.7
                adaptive_config.target_results = int(adaptive_config.target_results * 1.5)
        
        # 确保阈值在合理范围内
        adaptive_config = self._clamp_thresholds(adaptive_config)
        
        return adaptive_config
    
    def _get_historical_adjustment(self, query_type: QueryType) -> float:
        """基于历史数据获取调整值"""
        if query_type not in self.success_rates:
            return 0.0
        
        recent_rates = self.success_rates[query_type][-50:]  # 最近50次
        if not recent_rates:
            return 0.0
        
        avg_success_rate = np.mean(recent_rates)
        
        # 如果成功率低于60%，降低阈值
        if avg_success_rate < 0.6:
            return -0.05
        # 如果成功率高于90%，可以提高阈值
        elif avg_success_rate > 0.9:
            return 0.03
        
        return 0.0
    
    def _clamp_thresholds(self, config: ThresholdConfig) -> ThresholdConfig:
        """限制阈值在合理范围内"""
        config.min_score_threshold = max(0.001, min(0.5, config.min_score_threshold))
        config.semantic_threshold = max(0.1, min(0.9, config.semantic_threshold))
        config.keyword_threshold = max(0.1, min(0.9, config.keyword_threshold))
        config.quality_threshold = max(0.2, min(0.95, config.quality_threshold))
        config.target_results = max(1, min(100, config.target_results))
        
        return config
    
    def update_search_metrics(self, metrics: SearchMetrics):
        """更新搜索指标"""
        self.search_history.append(metrics)
        
        # 更新性能指标
        query_type = metrics.query_type
        self.performance_metrics[query_type].append({
            'avg_score': metrics.avg_score,
            'total_results': metrics.total_results,
            'search_time': metrics.search_time,
            'timestamp': metrics.timestamp
        })
        
        # 计算成功率
        success = self._calculate_success_rate(metrics)
        self.success_rates[query_type].append(success)
        
        # 限制历史数据大小
        if len(self.success_rates[query_type]) > 100:
            self.success_rates[query_type] = self.success_rates[query_type][-100:]
    
    def _calculate_success_rate(self, metrics: SearchMetrics) -> float:
        """计算搜索成功率"""
        success_score = 0.0
        
        # 结果数量评分 (30%)
        if metrics.total_results >= 1:
            result_score = min(1.0, metrics.total_results / 10.0)
            success_score += result_score * 0.3
        
        # 平均分数评分 (40%)
        if metrics.avg_score > 0:
            score_rating = min(1.0, metrics.avg_score / 0.5)
            success_score += score_rating * 0.4
        
        # 搜索时间评分 (20%)
        if metrics.search_time < 1.0:  # 1秒内
            time_score = 1.0
        elif metrics.search_time < 5.0:  # 5秒内
            time_score = 0.8
        else:
            time_score = 0.5
        success_score += time_score * 0.2
        
        # 用户满意度评分 (10%)
        if metrics.user_satisfaction is not None:
            success_score += metrics.user_satisfaction * 0.1
        else:
            success_score += 0.05  # 默认中等满意度
        
        return min(1.0, success_score)
    
    def get_threshold_recommendations(self, query_type: QueryType = None) -> Dict[str, Any]:
        """获取阈值优化建议"""
        recommendations = {
            'timestamp': time.time(),
            'overall_performance': {},
            'query_type_analysis': {},
            'optimization_suggestions': []
        }
        
        # 整体性能分析
        if self.search_history:
            recent_searches = list(self.search_history)[-1000:]  # 最近1000次搜索
            
            avg_results = np.mean([s.total_results for s in recent_searches])
            avg_score = np.mean([s.avg_score for s in recent_searches])
            avg_time = np.mean([s.search_time for s in recent_searches])
            
            recommendations['overall_performance'] = {
                'avg_results_per_query': avg_results,
                'avg_score': avg_score,
                'avg_search_time': avg_time,
                'total_searches': len(recent_searches)
            }
        
        # 查询类型分析
        for qtype in QueryType:
            type_metrics = [s for s in self.search_history if s.query_type == qtype]
            if type_metrics:
                success_rates = self.success_rates.get(qtype, [])
                avg_success = np.mean(success_rates) if success_rates else 0.0
                
                recommendations['query_type_analysis'][qtype.value] = {
                    'search_count': len(type_metrics),
                    'avg_success_rate': avg_success,
                    'current_thresholds': asdict(self.query_type_configs[qtype])
                }
                
                # 生成优化建议
                if avg_success < 0.6:
                    recommendations['optimization_suggestions'].append({
                        'query_type': qtype.value,
                        'issue': 'Low success rate',
                        'suggestion': 'Consider lowering thresholds to improve recall',
                        'priority': 'high'
                    })
                elif avg_success > 0.95:
                    recommendations['optimization_suggestions'].append({
                        'query_type': qtype.value,
                        'issue': 'Very high success rate',
                        'suggestion': 'Consider raising thresholds to improve precision',
                        'priority': 'low'
                    })
        
        return recommendations
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'base_config': asdict(self.base_config),
            'query_type_configs': {
                qtype.value: asdict(config) 
                for qtype, config in self.query_type_configs.items()
            },
            'threshold_adjustments': dict(self.threshold_adjustments),
            'success_rates': {
                qtype.value: rates[-100:] 
                for qtype, rates in self.success_rates.items()
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    def load_config(self):
        """从文件加载配置"""
        if not Path(self.config_file).exists():
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 加载基础配置
            if 'base_config' in config_data:
                self.base_config = ThresholdConfig(**config_data['base_config'])
            
            # 加载查询类型配置
            if 'query_type_configs' in config_data:
                for qtype_str, config_dict in config_data['query_type_configs'].items():
                    qtype = QueryType(qtype_str)
                    self.query_type_configs[qtype] = ThresholdConfig(**config_dict)
            
            # 加载调整参数
            if 'threshold_adjustments' in config_data:
                self.threshold_adjustments.update(config_data['threshold_adjustments'])
            
            # 加载成功率历史
            if 'success_rates' in config_data:
                for qtype_str, rates in config_data['success_rates'].items():
                    qtype = QueryType(qtype_str)
                    self.success_rates[qtype] = rates
            
        except Exception as e:
            print(f"⚠️ Failed to load config: {e}")

if __name__ == "__main__":
    # 示例用法
    print("🎛️ Dynamic Threshold Manager Test")
    print("=" * 50)
    
    # 初始化管理器
    manager = DynamicThresholdManager()
    
    # 测试不同类型的查询
    test_queries = [
        ("act_12m_eps_value", SearchContext.API_CALL),
        ("每股收益增长率", SearchContext.INTERACTIVE),
        ("earnings per share forecast", SearchContext.REAL_TIME),
        ("分析师对EBITDA的预测分析", SearchContext.BATCH_PROCESSING),
        ("ROE 净资产收益率", SearchContext.INTERACTIVE)
    ]
    
    for query, context in test_queries:
        print(f"\n🔍 Query: '{query}'")
        print(f"   Context: {context.value}")
        
        # 获取自适应阈值
        thresholds = manager.get_adaptive_thresholds(query, context)
        query_type = manager.classify_query_type(query)
        
        print(f"   Query Type: {query_type.value}")
        print(f"   Min Score Threshold: {thresholds.min_score_threshold:.3f}")
        print(f"   Semantic Threshold: {thresholds.semantic_threshold:.3f}")
        print(f"   Keyword Threshold: {thresholds.keyword_threshold:.3f}")
        print(f"   Target Results: {thresholds.target_results}")
        
        # 模拟搜索指标
        metrics = SearchMetrics(
            query=query,
            query_type=query_type,
            search_context=context,
            total_results=np.random.randint(1, 20),
            avg_score=np.random.uniform(0.1, 0.8),
            max_score=np.random.uniform(0.5, 1.0),
            min_score=np.random.uniform(0.01, 0.3),
            search_time=np.random.uniform(0.1, 2.0),
            applied_thresholds=asdict(thresholds)
        )
        
        # 更新指标
        manager.update_search_metrics(metrics)
    
    # 获取优化建议
    print(f"\n📊 Threshold Optimization Recommendations:")
    recommendations = manager.get_threshold_recommendations()
    
    print(f"Overall Performance:")
    for key, value in recommendations['overall_performance'].items():
        print(f"   {key}: {value:.3f}" if isinstance(value, float) else f"   {key}: {value}")
    
    print(f"\nOptimization Suggestions:")
    for suggestion in recommendations['optimization_suggestions']:
        print(f"   {suggestion['query_type']}: {suggestion['suggestion']} (Priority: {suggestion['priority']})")
    
    # 保存配置
    manager.save_config()
    print(f"\n💾 Configuration saved to {manager.config_file}")
    
    print("\n🎉 Dynamic threshold management system ready!")
