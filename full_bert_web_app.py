#!/usr/bin/env python3
"""
完整BERT模式Web应用
提供完整BERT搜索能力，第一次运行时预计算所有向量
"""

from flask import Flask, render_template, request, jsonify, session
import os
import time
import uuid
import pandas as pd
from pathlib import Path

# 导入完整BERT搜索引擎
from full_bert_engine import FullBertSearchEngine
from optimized_model_manager import CacheConfig
from dynamic_threshold_manager import SearchContext

app = Flask(__name__)
app.secret_key = 'full_bert_search_secret_key_2024'

# 全局变量
search_engine = None
data_loaded = False

# 缓存配置
cache_config = CacheConfig(
    cache_dir="cache",
    enable_cache=True,
    max_cache_size=2000  # 增加缓存大小以支持完整BERT模式
)

def initialize_search_engine():
    """初始化完整BERT搜索引擎"""
    global search_engine, data_loaded
    
    try:
        print("🚀 Initializing Full BERT Search Engine...")
        
        # 初始化完整BERT搜索引擎
        search_engine = FullBertSearchEngine(cache_config)
        search_engine.preload_models_full()
        
        # 加载默认数据集
        default_file = "split_files/USA_1_TOP3000.csv"
        if os.path.exists(default_file):
            # 使用较大的样本以展示完整BERT能力
            search_engine.load_data_and_build_full_index(
                default_file, 
                sample_size=2000,  # 增加样本大小
                batch_size=100
            )
            data_loaded = True
            print("✅ Full BERT search engine initialized successfully!")
        else:
            print(f"❌ Default data file not found: {default_file}")
            
    except Exception as e:
        print(f"❌ Failed to initialize search engine: {e}")
        search_engine = None
        data_loaded = False

@app.route('/')
def index():
    """主页"""
    # 获取可用的数据文件
    data_files = []
    split_files_dir = "split_files"
    if os.path.exists(split_files_dir):
        for filename in sorted(os.listdir(split_files_dir)):
            if filename.endswith('.csv'):
                # 解析文件名
                parts = filename.replace('.csv', '').split('_')
                if len(parts) >= 3:
                    region = parts[0]
                    version = parts[1]
                    dataset = '_'.join(parts[2:])

                    # 翻译地区名称
                    region_names = {
                        'USA': '美国',
                        'EUR': '欧洲',
                        'CHN': '中国',
                        'ASI': '亚洲',
                        'GLB': '全球'
                    }
                    
                    # 翻译数据集名称
                    dataset_names = {
                        'TOP3000': 'TOP3000 (大盘股)',
                        'TOP2500': 'TOP2500 (大中盘股)',
                        'TOP2000U': 'TOP2000U (大中盘股)',
                        'TOP1200': 'TOP1200 (大盘股)',
                        'TOP1000': 'TOP1000 (大盘股)',
                        'TOP800': 'TOP800 (大盘股)',
                        'TOP500': 'TOP500 (大盘股)',
                        'TOP400': 'TOP400 (大盘股)',
                        'TOP200': 'TOP200 (大盘股)',
                        'TOPSP500': 'TOP SP500 (标普500)',
                        'TOPDIV3000': 'TOP DIV3000 (高股息)',
                        'MINVOL1M': 'MIN VOL 1M (最小波动)',
                        'ILLIQUID_MINVOL1M': 'ILLIQUID MIN VOL 1M (非流动性最小波动)'
                    }

                    data_files.append({
                        'filename': filename,
                        'region': region_names.get(region, region),
                        'version': version,
                        'dataset': dataset,
                        'display_name': f"{region_names.get(region, region)} - {dataset_names.get(dataset, dataset)} (v{version}) [完整BERT]"
                    })

    return render_template('full_bert_index.html', data_files=data_files)

@app.route('/api/load_dataset', methods=['POST'])
def api_load_dataset():
    """加载指定数据集"""
    global search_engine, data_loaded
    
    try:
        data = request.get_json()
        filename = data.get('filename', '').strip()
        
        if not filename:
            return jsonify({
                'success': False,
                'error': 'No filename provided'
            })
        
        file_path = f"split_files/{filename}"
        if not os.path.exists(file_path):
            return jsonify({
                'success': False,
                'error': f'File not found: {filename}'
            })
        
        print(f"🔄 Loading new dataset: {filename}")
        
        # 重新初始化搜索引擎
        search_engine = FullBertSearchEngine(cache_config)
        search_engine.preload_models_full()
        
        # 加载新数据集
        df_size = len(pd.read_csv(file_path))
        sample_size = min(3000, df_size)  # 增加样本大小以展示完整BERT能力
        
        search_engine.load_data_and_build_full_index(
            file_path, 
            sample_size=sample_size,
            batch_size=100
        )
        
        data_loaded = True
        
        return jsonify({
            'success': True,
            'message': f'数据集 {filename} 加载成功',
            'records_loaded': sample_size,
            'total_records': df_size
        })
        
    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search', methods=['POST'])
def api_search():
    """搜索API"""
    try:
        if not data_loaded or not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })
        
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'No query provided'
            })
        
        # 获取会话ID
        session_id = session.get('session_id')
        if not session_id:
            session_id = str(uuid.uuid4())
            session['session_id'] = session_id
        
        # 执行完整BERT搜索（显示所有符合条件的结果）
        start_time = time.time()
        results = search_engine.search_full_bert(query, top_k=None, search_context=SearchContext.INTERACTIVE, session_id=session_id)
        search_time = time.time() - start_time

        # 获取会话统计
        stats = search_engine.get_session_stats(session_id)

        # 不进行自动翻译，让用户选择是否翻译
        translation_time = 0.0

        # 格式化结果（不翻译版本）
        formatted_results = []
        for result in results:
            formatted_results.append({
                'id': result.id,
                'description': result.description,
                'description_translated': None,  # 初始不翻译
                'dataset_name': result.dataset_name,
                'dataset_name_translated': result.dataset_name,
                'category_name': result.category_name,
                'category_name_translated': result.category_name,
                'subcategory_name': result.subcategory_name,
                'subcategory_name_translated': result.subcategory_name,
                'overall_score': float(round(result.overall_score, 4)),
                'semantic_score': float(round(result.semantic_score, 4)),
                'keyword_score': float(round(result.keyword_score, 4)),
                'matched_columns': result.matched_columns,
                'matched_keywords': result.matched_keywords[:10],
                'region': result.region,
                'universe': result.universe,
                'delay': result.delay
            })

        return jsonify({
            'success': True,
            'results': formatted_results,
            'search_time': round(search_time, 3),
            'translation_time': round(translation_time, 3),
            'total_time': round(search_time + translation_time, 3),
            'total_results': len(formatted_results),
            'session_stats': stats,
            'auto_translated': False,
            'search_mode': 'full_bert_mode'
        })
        
    except Exception as e:
        print(f"❌ Search error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/translate', methods=['POST'])
def api_translate():
    """按需翻译API"""
    try:
        if not search_engine:
            return jsonify({
                'success': False,
                'error': 'Search engine not initialized'
            })
        
        data = request.get_json()
        texts = data.get('texts', [])
        
        if not texts:
            return jsonify({
                'success': False,
                'error': 'No texts to translate'
            })
        
        # 批量翻译
        start_time = time.time()
        translated_texts = search_engine.translator.batch_translate(texts, 'description')
        translation_time = time.time() - start_time
        
        return jsonify({
            'success': True,
            'translations': translated_texts,
            'translation_time': round(translation_time, 3),
            'count': len(translated_texts)
        })
        
    except Exception as e:
        print(f"❌ Translation error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/status')
def api_status():
    """获取系统状态"""
    if search_engine:
        status = search_engine.get_initialization_status()
        status['data_loaded'] = data_loaded
        status['search_mode'] = 'full_bert_mode'
        return jsonify(status)
    else:
        return jsonify({
            'status': 'not_initialized',
            'progress': 0,
            'message': 'Search engine not initialized',
            'data_loaded': False,
            'search_mode': 'full_bert_mode'
        })

if __name__ == '__main__':
    print("🌐 Starting Full BERT Search Web App...")
    print("📱 Access the app at: http://localhost:5000")
    print("⚡ Features: Full BERT Power + Smart Translation + Vector Caching")
    print("🎯 Using full BERT capabilities with pre-computed vectors")
    
    # 在后台线程中初始化搜索引擎
    import threading
    init_thread = threading.Thread(target=initialize_search_engine, daemon=True)
    init_thread.start()
    
    app.run(host='0.0.0.0', port=5000, debug=False)
