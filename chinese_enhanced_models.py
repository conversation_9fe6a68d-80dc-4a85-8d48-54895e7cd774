#!/usr/bin/env python3
"""
中文增强模型模块
集成专业的中文BERT、RoBERTa等模型，大幅提升中文支持能力
"""

import os
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
import json
import re
from dataclasses import dataclass
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 中文NLP工具
import jieba
import jieba.posseg as pseg
import jieba.analyse
from collections import defaultdict, Counter

# 尝试导入高级中文模型
try:
    import torch
    from transformers import (
        BertTokenizer, BertModel,
        AutoTokenizer, AutoModel,
        pipeline
    )
    ADVANCED_MODELS_AVAILABLE = True
except ImportError:
    print("⚠️ Advanced models not available, using enhanced jieba-based processing")
    ADVANCED_MODELS_AVAILABLE = False

@dataclass
class ChineseEntity:
    """中文实体"""
    text: str
    entity_type: str
    start_pos: int
    end_pos: int
    confidence: float
    context: str

@dataclass
class ChineseSemanticFeature:
    """中文语义特征"""
    text: str
    keywords: List[Tuple[str, float]]
    entities: List[ChineseEntity]
    semantic_roles: Dict[str, List[str]]
    sentiment: str
    topics: List[str]
    embeddings: Optional[np.ndarray] = None

class ChineseFinancialNER:
    """中文金融命名实体识别"""
    
    def __init__(self):
        self.entity_patterns = self._build_entity_patterns()
        self.financial_lexicon = self._build_financial_lexicon()
        
    def _build_entity_patterns(self) -> Dict[str, List[re.Pattern]]:
        """构建实体识别模式"""
        patterns = {
            'FINANCIAL_METRIC': [
                re.compile(r'(每股收益|净资产收益率|总资产收益率|毛利率|净利率|资产负债率)'),
                re.compile(r'(市盈率|市净率|市销率|企业价值倍数|股息收益率)'),
                re.compile(r'(营业收入|净利润|毛利润|经营现金流|自由现金流)'),
                re.compile(r'(EBITDA|EBIT|ROE|ROA|PE|PB|PS|EPS)'),
            ],
            'TIME_PERIOD': [
                re.compile(r'(\d{4}年|\d{1,2}月|\d{1,2}日)'),
                re.compile(r'(第[一二三四]季度|上半年|下半年|年初|年末|季末)'),
                re.compile(r'(同比|环比|年度|季度|月度)'),
            ],
            'FINANCIAL_INSTITUTION': [
                re.compile(r'(银行|证券|保险|基金|信托|期货)公司'),
                re.compile(r'(中国银行|工商银行|建设银行|农业银行|交通银行)'),
                re.compile(r'(中信证券|海通证券|国泰君安|华泰证券|广发证券)'),
            ],
            'INDUSTRY': [
                re.compile(r'(房地产|制造业|金融业|信息技术|医疗保健|消费品)'),
                re.compile(r'(能源|材料|工业|公用事业|电信服务|可选消费)'),
            ],
            'CURRENCY_AMOUNT': [
                re.compile(r'([¥$€£]\d+\.?\d*[万亿千百十]?)'),
                re.compile(r'(\d+\.?\d*[万亿千百十]?[元美元欧元英镑])'),
            ],
            'PERCENTAGE': [
                re.compile(r'(\d+\.?\d*%)'),
                re.compile(r'(百分之\d+\.?\d*)'),
            ],
            'ANALYST_ACTION': [
                re.compile(r'(买入|卖出|持有|增持|减持|中性|推荐|不推荐)'),
                re.compile(r'(上调|下调|维持|目标价|评级|预测)'),
            ]
        }
        return patterns
    
    def _build_financial_lexicon(self) -> Dict[str, Dict[str, float]]:
        """构建金融词汇库"""
        return {
            'positive_indicators': {
                '增长': 0.8, '上涨': 0.7, '提升': 0.6, '改善': 0.6, '优化': 0.5,
                '强劲': 0.8, '稳健': 0.6, '良好': 0.5, '积极': 0.6, '乐观': 0.7
            },
            'negative_indicators': {
                '下降': -0.7, '下跌': -0.8, '恶化': -0.8, '减少': -0.6, '萎缩': -0.7,
                '疲软': -0.6, '低迷': -0.7, '困难': -0.6, '风险': -0.5, '担忧': -0.6
            },
            'neutral_indicators': {
                '稳定': 0.0, '持平': 0.0, '维持': 0.0, '保持': 0.0, '平稳': 0.0
            }
        }
    
    def extract_entities(self, text: str) -> List[ChineseEntity]:
        """提取中文实体"""
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern in patterns:
                for match in pattern.finditer(text):
                    entity = ChineseEntity(
                        text=match.group(),
                        entity_type=entity_type,
                        start_pos=match.start(),
                        end_pos=match.end(),
                        confidence=0.9,
                        context=text[max(0, match.start()-20):match.end()+20]
                    )
                    entities.append(entity)
        
        return entities

class ChineseSemanticAnalyzer:
    """中文语义分析器"""
    
    def __init__(self):
        self.ner = ChineseFinancialNER()
        self.topic_keywords = self._build_topic_keywords()
        
        # 初始化jieba分析器
        jieba.analyse.set_stop_words('stopwords.txt')  # 如果有停用词文件
        
    def _build_topic_keywords(self) -> Dict[str, List[str]]:
        """构建主题关键词"""
        return {
            '盈利能力': ['净利润', '毛利率', '净利率', '每股收益', 'ROE', 'ROA', '盈利'],
            '偿债能力': ['资产负债率', '流动比率', '速动比率', '利息保障倍数', '债务'],
            '营运能力': ['存货周转率', '应收账款周转率', '总资产周转率', '营运', '周转'],
            '成长能力': ['营收增长率', '净利润增长率', '总资产增长率', '增长', '成长'],
            '估值水平': ['市盈率', '市净率', '市销率', 'PEG', '估值', '价值'],
            '现金流': ['经营现金流', '自由现金流', '现金流量', '现金', '流动性'],
            '分析师观点': ['买入', '卖出', '持有', '目标价', '评级', '预测', '分析师'],
            '行业分析': ['行业', '板块', '龙头', '竞争', '市场份额', '产业链'],
            '风险因素': ['风险', '不确定性', '挑战', '压力', '困难', '威胁'],
            '投资建议': ['建议', '推荐', '配置', '投资', '策略', '机会']
        }
    
    def analyze_sentiment(self, text: str) -> Tuple[str, float]:
        """分析情感倾向"""
        positive_score = 0.0
        negative_score = 0.0
        
        # 使用金融词汇库分析情感
        words = jieba.lcut(text)
        
        for word in words:
            for sentiment_type, word_scores in self.ner.financial_lexicon.items():
                if word in word_scores:
                    score = word_scores[word]
                    if score > 0:
                        positive_score += score
                    elif score < 0:
                        negative_score += abs(score)
        
        # 计算总体情感
        total_score = positive_score - negative_score
        
        if total_score > 0.3:
            sentiment = 'positive'
        elif total_score < -0.3:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        confidence = min(abs(total_score), 1.0)
        
        return sentiment, confidence
    
    def extract_topics(self, text: str, top_k: int = 3) -> List[Tuple[str, float]]:
        """提取主题"""
        topic_scores = defaultdict(float)
        words = jieba.lcut(text.lower())
        
        for topic, keywords in self.topic_keywords.items():
            score = 0.0
            for keyword in keywords:
                if keyword in text.lower():
                    score += 1.0
                # 检查词汇匹配
                for word in words:
                    if keyword in word or word in keyword:
                        score += 0.5
            
            if score > 0:
                topic_scores[topic] = score / len(keywords)  # 标准化分数
        
        # 排序并返回top_k
        sorted_topics = sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_topics[:top_k]
    
    def extract_semantic_roles(self, text: str) -> Dict[str, List[str]]:
        """提取语义角色"""
        roles = defaultdict(list)
        
        # 使用词性标注提取语义角色
        words_with_pos = pseg.lcut(text)
        
        current_subject = []
        current_predicate = []
        current_object = []
        
        for word, pos in words_with_pos:
            if pos.startswith('n'):  # 名词作为主语或宾语
                if not current_predicate:  # 谓语前的名词作为主语
                    current_subject.append(word)
                else:  # 谓语后的名词作为宾语
                    current_object.append(word)
            elif pos.startswith('v'):  # 动词作为谓语
                current_predicate.append(word)
            elif word in ['，', '。', '；', '！', '？']:  # 句子结束
                if current_subject:
                    roles['subject'].extend(current_subject)
                if current_predicate:
                    roles['predicate'].extend(current_predicate)
                if current_object:
                    roles['object'].extend(current_object)
                
                # 重置
                current_subject = []
                current_predicate = []
                current_object = []
        
        # 处理最后一个句子
        if current_subject:
            roles['subject'].extend(current_subject)
        if current_predicate:
            roles['predicate'].extend(current_predicate)
        if current_object:
            roles['object'].extend(current_object)
        
        return dict(roles)
    
    def analyze_text(self, text: str) -> ChineseSemanticFeature:
        """完整的中文文本语义分析"""
        if not text or pd.isna(text):
            return ChineseSemanticFeature(
                text="", keywords=[], entities=[], 
                semantic_roles={}, sentiment="neutral", topics=[]
            )
        
        text = str(text)
        
        # 提取关键词
        keywords = jieba.analyse.extract_tags(text, topK=15, withWeight=True)
        
        # 提取实体
        entities = self.ner.extract_entities(text)
        
        # 语义角色分析
        semantic_roles = self.extract_semantic_roles(text)
        
        # 情感分析
        sentiment, _ = self.analyze_sentiment(text)
        
        # 主题提取
        topics = [topic for topic, score in self.extract_topics(text)]
        
        return ChineseSemanticFeature(
            text=text,
            keywords=keywords,
            entities=entities,
            semantic_roles=semantic_roles,
            sentiment=sentiment,
            topics=topics
        )

class ChineseEnhancedEmbedding:
    """中文增强嵌入模型"""
    
    def __init__(self, model_configs: Optional[Dict[str, Any]] = None):
        self.model_configs = model_configs or self._get_default_configs()
        self.models = {}
        self.semantic_analyzer = ChineseSemanticAnalyzer()
        
    def _get_default_configs(self) -> Dict[str, Any]:
        """获取默认中文模型配置"""
        return {
            'chinese_bert': {
                'model_name': 'bert-base-chinese',
                'weight': 0.4,
                'enabled': True
            },
            'chinese_roberta': {
                'model_name': 'hfl/chinese-roberta-wwm-ext',
                'weight': 0.4,
                'enabled': True
            },
            'multilingual_bert': {
                'model_name': 'bert-base-multilingual-cased',
                'weight': 0.2,
                'enabled': True
            }
        }
    
    def load_models(self):
        """加载中文模型"""
        if not ADVANCED_MODELS_AVAILABLE:
            print("⚠️ Using enhanced jieba-based embeddings")
            return self._init_jieba_embeddings()
        
        print("📥 Loading Chinese enhanced models...")
        
        for model_key, config in self.model_configs.items():
            if not config.get('enabled', True):
                continue
            
            try:
                model_name = config['model_name']
                print(f"   Loading {model_key}: {model_name}")
                
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModel.from_pretrained(model_name)
                
                self.models[model_key] = {
                    'tokenizer': tokenizer,
                    'model': model,
                    'weight': config['weight']
                }
                
                print(f"   ✅ {model_key} loaded successfully")
                
            except Exception as e:
                print(f"   ❌ Failed to load {model_key}: {e}")
                config['enabled'] = False
        
        print(f"✅ Loaded {len(self.models)} Chinese models")
    
    def _init_jieba_embeddings(self):
        """初始化基于jieba的嵌入"""
        from sklearn.feature_extraction.text import TfidfVectorizer
        
        # 创建中文优化的TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=20000,
            ngram_range=(1, 3),
            min_df=1,
            max_df=0.9,
            tokenizer=lambda x: jieba.lcut(x),
            token_pattern=None
        )
        
        print("✅ Jieba-based embeddings initialized")
    
    def encode_chinese_text(self, text: str) -> Dict[str, np.ndarray]:
        """编码中文文本"""
        if not ADVANCED_MODELS_AVAILABLE:
            return self._encode_with_jieba(text)
        
        encodings = {}
        
        for model_key, model_info in self.models.items():
            try:
                tokenizer = model_info['tokenizer']
                model = model_info['model']
                
                # 分词和编码
                inputs = tokenizer(text, return_tensors='pt', 
                                 truncation=True, max_length=512, padding=True)
                
                with torch.no_grad():
                    outputs = model(**inputs)
                    # 使用平均池化
                    embeddings = outputs.last_hidden_state.mean(dim=1).numpy()[0]
                
                encodings[model_key] = embeddings
                
            except Exception as e:
                print(f"⚠️ Encoding failed for {model_key}: {e}")
        
        return encodings
    
    def _encode_with_jieba(self, text: str) -> Dict[str, np.ndarray]:
        """使用jieba进行编码"""
        # 分词
        words = jieba.lcut(text)
        
        # 创建简单的词向量（基于词频和位置）
        word_freq = Counter(words)
        vocab_size = len(word_freq)
        
        # 创建简单的嵌入向量
        embedding = np.zeros(300)  # 300维向量
        
        for i, (word, freq) in enumerate(word_freq.most_common()):
            # 基于词频和位置的简单编码
            word_hash = hash(word) % 300
            embedding[word_hash] += freq * (1.0 / (i + 1))
        
        # 标准化
        if np.linalg.norm(embedding) > 0:
            embedding = embedding / np.linalg.norm(embedding)
        
        return {'jieba_embedding': embedding}
    
    def enhanced_chinese_search(self, query: str, documents: List[str], 
                               top_k: int = 10) -> List[Tuple[int, float, Dict[str, Any]]]:
        """增强的中文搜索"""
        print(f"🔍 Enhanced Chinese search for: '{query}'")
        
        # 分析查询
        query_analysis = self.semantic_analyzer.analyze_text(query)
        query_embeddings = self.encode_chinese_text(query)
        
        results = []
        
        for doc_idx, doc_text in enumerate(documents):
            # 分析文档
            doc_analysis = self.semantic_analyzer.analyze_text(doc_text)
            doc_embeddings = self.encode_chinese_text(doc_text)
            
            # 计算多维度相似度
            similarity_scores = {}
            
            # 1. 语义相似度
            semantic_score = self._calculate_semantic_similarity(
                query_embeddings, doc_embeddings
            )
            similarity_scores['semantic'] = semantic_score
            
            # 2. 关键词相似度
            keyword_score = self._calculate_keyword_similarity(
                query_analysis.keywords, doc_analysis.keywords
            )
            similarity_scores['keyword'] = keyword_score
            
            # 3. 实体相似度
            entity_score = self._calculate_entity_similarity(
                query_analysis.entities, doc_analysis.entities
            )
            similarity_scores['entity'] = entity_score
            
            # 4. 主题相似度
            topic_score = self._calculate_topic_similarity(
                query_analysis.topics, doc_analysis.topics
            )
            similarity_scores['topic'] = topic_score
            
            # 综合分数
            overall_score = (
                semantic_score * 0.4 +
                keyword_score * 0.3 +
                entity_score * 0.2 +
                topic_score * 0.1
            )
            
            if overall_score > 0.01:
                results.append((doc_idx, overall_score, {
                    'similarity_scores': similarity_scores,
                    'query_analysis': query_analysis,
                    'doc_analysis': doc_analysis
                }))
        
        # 排序并返回top_k
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]
    
    def _calculate_semantic_similarity(self, query_emb: Dict[str, np.ndarray], 
                                     doc_emb: Dict[str, np.ndarray]) -> float:
        """计算语义相似度"""
        total_score = 0.0
        total_weight = 0.0
        
        for model_key in query_emb:
            if model_key in doc_emb:
                try:
                    q_vec = query_emb[model_key]
                    d_vec = doc_emb[model_key]
                    
                    similarity = np.dot(q_vec, d_vec) / (
                        np.linalg.norm(q_vec) * np.linalg.norm(d_vec) + 1e-8
                    )
                    
                    weight = self.model_configs.get(model_key, {}).get('weight', 0.33)
                    total_score += similarity * weight
                    total_weight += weight
                    
                except Exception:
                    continue
        
        return total_score / (total_weight + 1e-8)
    
    def _calculate_keyword_similarity(self, query_kw: List[Tuple[str, float]], 
                                    doc_kw: List[Tuple[str, float]]) -> float:
        """计算关键词相似度"""
        if not query_kw or not doc_kw:
            return 0.0
        
        query_words = {word: weight for word, weight in query_kw}
        doc_words = {word: weight for word, weight in doc_kw}
        
        common_words = set(query_words.keys()) & set(doc_words.keys())
        
        if not common_words:
            return 0.0
        
        similarity = sum(
            min(query_words[word], doc_words[word]) 
            for word in common_words
        )
        
        max_possible = sum(query_words.values())
        
        return similarity / (max_possible + 1e-8)
    
    def _calculate_entity_similarity(self, query_entities: List[ChineseEntity], 
                                   doc_entities: List[ChineseEntity]) -> float:
        """计算实体相似度"""
        if not query_entities or not doc_entities:
            return 0.0
        
        query_entity_texts = {e.text.lower() for e in query_entities}
        doc_entity_texts = {e.text.lower() for e in doc_entities}
        
        common_entities = query_entity_texts & doc_entity_texts
        
        if not common_entities:
            return 0.0
        
        return len(common_entities) / len(query_entity_texts)
    
    def _calculate_topic_similarity(self, query_topics: List[str], 
                                  doc_topics: List[str]) -> float:
        """计算主题相似度"""
        if not query_topics or not doc_topics:
            return 0.0
        
        common_topics = set(query_topics) & set(doc_topics)
        
        if not common_topics:
            return 0.0
        
        return len(common_topics) / len(query_topics)

if __name__ == "__main__":
    # 测试中文增强模型
    print("🇨🇳 Testing Chinese Enhanced Models...")
    
    # 初始化中文增强嵌入
    chinese_model = ChineseEnhancedEmbedding()
    chinese_model.load_models()
    
    # 测试文档
    test_docs = [
        "公司2023年每股收益为2.5元，同比增长15%，净资产收益率达到18%",
        "分析师预测该股票目标价为50元，给予买入评级",
        "EBITDA同比增长20%，营业收入达到100亿元",
        "The company's earnings per share increased by 15% year-over-year"
    ]
    
    # 测试查询
    test_queries = [
        "每股收益增长情况",
        "分析师评级和目标价",
        "EBITDA和营收数据",
        "盈利能力分析"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = chinese_model.enhanced_chinese_search(query, test_docs, top_k=3)
        
        for rank, (doc_idx, score, details) in enumerate(results, 1):
            print(f"{rank}. Doc {doc_idx} (Score: {score:.4f})")
            print(f"   Text: {test_docs[doc_idx][:60]}...")
            print(f"   Scores: {details['similarity_scores']}")
            print()
