{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 抓Brain平台数据专用\n", "\n", "- 抓Operators\n", "- 抓所有datasets和datafields\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## login 账户"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import requests\n", "from os import environ\n", "import os\n", "from time import sleep\n", "import time\n", "import json\n", "import pandas as pd\n", "\n", "\n", "def login():\n", "    \n", "    username = \"\"\n", "    password = \"\"\n", " \n", "    # Create a session to persistently store the headers\n", "    s = requests.Session()\n", " \n", "    # Save credentials into session\n", "    s.auth = (username, password)\n", " \n", "    # Send a POST request to the /authentication API\n", "    response = s.post('https://api.worldquantbrain.com/authentication')\n", "    print(response.content)\n", "    return s  "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"YK49234\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"BRAIN_LABS\",\"BRAIN_LABS_JUPYTER_LAB\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"VISUALIZATION\",\"WORKDAY\"]}'\n"]}], "source": ["import pandas as pd\n", "\n", "user_id = \"\"\n", "s = login()\n", "res = s.get(\"https://api.worldquantbrain.com/operators\")\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(res.json())[['name']]\n", "df.to_csv(f\"{user_id}_operators.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 抓 Operators\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"YK49234\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"BRAIN_LABS\",\"BRAIN_LABS_JUPYTER_LAB\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"VISUALIZATION\",\"WORKDAY\"]}'\n"]}], "source": ["s=login()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# create a function to get all operators, return is a list of dict, url is https://api.worldquantbrain.com/operators\n", "def get_operators(s):\n", "    response = s.get('https://api.worldquantbrain.com/operators')\n", "    operators = response.json()\n", "    return operators\n", "\n", "# get_operators(s)\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "SSLError", "evalue": "HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Max retries exceeded with url: /authentication (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mSSLEOFError\u001b[0m                               Traceback (most recent call last)", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connectionpool.py:466\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    465\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 466\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_validate_conn(conn)\n\u001b[1;32m    467\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connectionpool.py:1095\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[0;34m(self, conn)\u001b[0m\n\u001b[1;32m   1094\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_closed:\n\u001b[0;32m-> 1095\u001b[0m     conn\u001b[38;5;241m.\u001b[39mconnect()\n\u001b[1;32m   1097\u001b[0m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connection.py:730\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    728\u001b[0m server_hostname_rm_dot \u001b[38;5;241m=\u001b[39m server_hostname\u001b[38;5;241m.\u001b[39mrstrip(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 730\u001b[0m sock_and_verified \u001b[38;5;241m=\u001b[39m _ssl_wrap_socket_and_match_hostname(\n\u001b[1;32m    731\u001b[0m     sock\u001b[38;5;241m=\u001b[39msock,\n\u001b[1;32m    732\u001b[0m     cert_reqs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcert_reqs,\n\u001b[1;32m    733\u001b[0m     ssl_version\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mssl_version,\n\u001b[1;32m    734\u001b[0m     ssl_minimum_version\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mssl_minimum_version,\n\u001b[1;32m    735\u001b[0m     ssl_maximum_version\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mssl_maximum_version,\n\u001b[1;32m    736\u001b[0m     ca_certs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mca_certs,\n\u001b[1;32m    737\u001b[0m     ca_cert_dir\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mca_cert_dir,\n\u001b[1;32m    738\u001b[0m     ca_cert_data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mca_cert_data,\n\u001b[1;32m    739\u001b[0m     cert_file\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcert_file,\n\u001b[1;32m    740\u001b[0m     key_file\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkey_file,\n\u001b[1;32m    741\u001b[0m     key_password\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mkey_password,\n\u001b[1;32m    742\u001b[0m     server_hostname\u001b[38;5;241m=\u001b[39mserver_hostname_rm_dot,\n\u001b[1;32m    743\u001b[0m     ssl_context\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mssl_context,\n\u001b[1;32m    744\u001b[0m     tls_in_tls\u001b[38;5;241m=\u001b[39mtls_in_tls,\n\u001b[1;32m    745\u001b[0m     assert_hostname\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39massert_hostname,\n\u001b[1;32m    746\u001b[0m     assert_fingerprint\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39massert_fingerprint,\n\u001b[1;32m    747\u001b[0m )\n\u001b[1;32m    748\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m sock_and_verified\u001b[38;5;241m.\u001b[39msocket\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connection.py:909\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_and_match_hostname\u001b[0;34m(sock, cert_reqs, ssl_version, ssl_minimum_version, ssl_maximum_version, cert_file, key_file, key_password, ca_certs, ca_cert_dir, ca_cert_data, assert_hostname, assert_fingerprint, server_hostname, ssl_context, tls_in_tls)\u001b[0m\n\u001b[1;32m    907\u001b[0m         server_hostname \u001b[38;5;241m=\u001b[39m normalized\n\u001b[0;32m--> 909\u001b[0m ssl_sock \u001b[38;5;241m=\u001b[39m ssl_wrap_socket(\n\u001b[1;32m    910\u001b[0m     sock\u001b[38;5;241m=\u001b[39msock,\n\u001b[1;32m    911\u001b[0m     keyfile\u001b[38;5;241m=\u001b[39mkey_file,\n\u001b[1;32m    912\u001b[0m     certfile\u001b[38;5;241m=\u001b[39mcert_file,\n\u001b[1;32m    913\u001b[0m     key_password\u001b[38;5;241m=\u001b[39mkey_password,\n\u001b[1;32m    914\u001b[0m     ca_certs\u001b[38;5;241m=\u001b[39mca_certs,\n\u001b[1;32m    915\u001b[0m     ca_cert_dir\u001b[38;5;241m=\u001b[39mca_cert_dir,\n\u001b[1;32m    916\u001b[0m     ca_cert_data\u001b[38;5;241m=\u001b[39mca_cert_data,\n\u001b[1;32m    917\u001b[0m     server_hostname\u001b[38;5;241m=\u001b[39mserver_hostname,\n\u001b[1;32m    918\u001b[0m     ssl_context\u001b[38;5;241m=\u001b[39mcontext,\n\u001b[1;32m    919\u001b[0m     tls_in_tls\u001b[38;5;241m=\u001b[39mtls_in_tls,\n\u001b[1;32m    920\u001b[0m )\n\u001b[1;32m    922\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/util/ssl_.py:469\u001b[0m, in \u001b[0;36mssl_wrap_socket\u001b[0;34m(sock, keyfile, certfile, cert_reqs, ca_certs, server_hostname, ssl_version, ciphers, ssl_context, ca_cert_dir, key_password, ca_cert_data, tls_in_tls)\u001b[0m\n\u001b[1;32m    467\u001b[0m context\u001b[38;5;241m.\u001b[39mset_alpn_protocols(ALPN_PROTOCOLS)\n\u001b[0;32m--> 469\u001b[0m ssl_sock \u001b[38;5;241m=\u001b[39m _ssl_wrap_socket_impl(sock, context, tls_in_tls, server_hostname)\n\u001b[1;32m    470\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ssl_sock\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/util/ssl_.py:513\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_impl\u001b[0;34m(sock, ssl_context, tls_in_tls, server_hostname)\u001b[0m\n\u001b[1;32m    511\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m SSLTransport(sock, ssl_context, server_hostname)\n\u001b[0;32m--> 513\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ssl_context\u001b[38;5;241m.\u001b[39mwrap_socket(sock, server_hostname\u001b[38;5;241m=\u001b[39mserver_hostname)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/ssl.py:455\u001b[0m, in \u001b[0;36mSSLContext.wrap_socket\u001b[0;34m(self, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, session)\u001b[0m\n\u001b[1;32m    449\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrap_socket\u001b[39m(\u001b[38;5;28mself\u001b[39m, sock, server_side\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    450\u001b[0m                 do_handshake_on_connect\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    451\u001b[0m                 suppress_ragged_eofs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    452\u001b[0m                 server_hostname\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, session\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m    453\u001b[0m     \u001b[38;5;66;03m# SSLSocket class handles server_hostname encoding before it calls\u001b[39;00m\n\u001b[1;32m    454\u001b[0m     \u001b[38;5;66;03m# ctx._wrap_socket()\u001b[39;00m\n\u001b[0;32m--> 455\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msslsocket_class\u001b[38;5;241m.\u001b[39m_create(\n\u001b[1;32m    456\u001b[0m         sock\u001b[38;5;241m=\u001b[39msock,\n\u001b[1;32m    457\u001b[0m         server_side\u001b[38;5;241m=\u001b[39mserver_side,\n\u001b[1;32m    458\u001b[0m         do_handshake_on_connect\u001b[38;5;241m=\u001b[39mdo_handshake_on_connect,\n\u001b[1;32m    459\u001b[0m         suppress_ragged_eofs\u001b[38;5;241m=\u001b[39msuppress_ragged_eofs,\n\u001b[1;32m    460\u001b[0m         server_hostname\u001b[38;5;241m=\u001b[39mserver_hostname,\n\u001b[1;32m    461\u001b[0m         context\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m    462\u001b[0m         session\u001b[38;5;241m=\u001b[39msession\n\u001b[1;32m    463\u001b[0m     )\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/ssl.py:1042\u001b[0m, in \u001b[0;36mSSLSocket._create\u001b[0;34m(cls, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, context, session)\u001b[0m\n\u001b[1;32m   1041\u001b[0m                 \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdo_handshake_on_connect should not be specified for non-blocking sockets\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m-> 1042\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdo_handshake()\n\u001b[1;32m   1043\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/ssl.py:1320\u001b[0m, in \u001b[0;36mSSLSocket.do_handshake\u001b[0;34m(self, block)\u001b[0m\n\u001b[1;32m   1319\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msettimeout(\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m-> 1320\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_sslobj\u001b[38;5;241m.\u001b[39mdo_handshake()\n\u001b[1;32m   1321\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[0;31mSSLEOFError\u001b[0m: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connectionpool.py:789\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    788\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 789\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_request(\n\u001b[1;32m    790\u001b[0m     conn,\n\u001b[1;32m    791\u001b[0m     method,\n\u001b[1;32m    792\u001b[0m     url,\n\u001b[1;32m    793\u001b[0m     timeout\u001b[38;5;241m=\u001b[39mtimeout_obj,\n\u001b[1;32m    794\u001b[0m     body\u001b[38;5;241m=\u001b[39mbody,\n\u001b[1;32m    795\u001b[0m     headers\u001b[38;5;241m=\u001b[39mheaders,\n\u001b[1;32m    796\u001b[0m     chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[1;32m    797\u001b[0m     retries\u001b[38;5;241m=\u001b[39mretries,\n\u001b[1;32m    798\u001b[0m     response_conn\u001b[38;5;241m=\u001b[39mresponse_conn,\n\u001b[1;32m    799\u001b[0m     preload_content\u001b[38;5;241m=\u001b[39mpreload_content,\n\u001b[1;32m    800\u001b[0m     decode_content\u001b[38;5;241m=\u001b[39mdecode_content,\n\u001b[1;32m    801\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw,\n\u001b[1;32m    802\u001b[0m )\n\u001b[1;32m    804\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connectionpool.py:490\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    489\u001b[0m         new_e \u001b[38;5;241m=\u001b[39m _wrap_proxy_error(new_e, conn\u001b[38;5;241m.\u001b[39mproxy\u001b[38;5;241m.\u001b[39mscheme)\n\u001b[0;32m--> 490\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m new_e\n\u001b[1;32m    492\u001b[0m \u001b[38;5;66;03m# conn.request() calls http.client.*.request, not the method in\u001b[39;00m\n\u001b[1;32m    493\u001b[0m \u001b[38;5;66;03m# urllib3.request. It also calls makefile (recv) on the socket.\u001b[39;00m\n", "\u001b[0;31mSSLError\u001b[0m: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/adapters.py:486\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    485\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 486\u001b[0m     resp \u001b[38;5;241m=\u001b[39m conn\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[1;32m    487\u001b[0m         method\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mmethod,\n\u001b[1;32m    488\u001b[0m         url\u001b[38;5;241m=\u001b[39murl,\n\u001b[1;32m    489\u001b[0m         body\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mbody,\n\u001b[1;32m    490\u001b[0m         headers\u001b[38;5;241m=\u001b[39mrequest\u001b[38;5;241m.\u001b[39mheaders,\n\u001b[1;32m    491\u001b[0m         redirect\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    492\u001b[0m         assert_same_host\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    493\u001b[0m         preload_content\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    494\u001b[0m         decode_content\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    495\u001b[0m         retries\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_retries,\n\u001b[1;32m    496\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[1;32m    497\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[1;32m    498\u001b[0m     )\n\u001b[1;32m    500\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/connectionpool.py:843\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    841\u001b[0m     new_e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, new_e)\n\u001b[0;32m--> 843\u001b[0m retries \u001b[38;5;241m=\u001b[39m retries\u001b[38;5;241m.\u001b[39mincrement(\n\u001b[1;32m    844\u001b[0m     method, url, error\u001b[38;5;241m=\u001b[39mnew_e, _pool\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m, _stacktrace\u001b[38;5;241m=\u001b[39msys\u001b[38;5;241m.\u001b[39mexc_info()[\u001b[38;5;241m2\u001b[39m]\n\u001b[1;32m    845\u001b[0m )\n\u001b[1;32m    846\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/urllib3/util/retry.py:519\u001b[0m, in \u001b[0;36mRetry.increment\u001b[0;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[1;32m    518\u001b[0m     reason \u001b[38;5;241m=\u001b[39m error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause)\n\u001b[0;32m--> 519\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, reason) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON>son\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[1;32m    521\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[0;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Max retries exceeded with url: /authentication (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m s\u001b[38;5;241m=\u001b[39mlogin()\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28mlen\u001b[39m(get_operators(s))\n", "Cell \u001b[0;32mIn[6], line 22\u001b[0m, in \u001b[0;36mlogin\u001b[0;34m()\u001b[0m\n\u001b[1;32m     19\u001b[0m s\u001b[38;5;241m.\u001b[39mauth \u001b[38;5;241m=\u001b[39m (username, password)\n\u001b[1;32m     21\u001b[0m \u001b[38;5;66;03m# Send a POST request to the /authentication API\u001b[39;00m\n\u001b[0;32m---> 22\u001b[0m response \u001b[38;5;241m=\u001b[39m s\u001b[38;5;241m.\u001b[39mpost(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://api.worldquantbrain.com/authentication\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28mprint\u001b[39m(response\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m     24\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m s\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/sessions.py:637\u001b[0m, in \u001b[0;36mSession.post\u001b[0;34m(self, url, data, json, **kwargs)\u001b[0m\n\u001b[1;32m    626\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\u001b[38;5;28mself\u001b[39m, url, data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, json\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    627\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a POST request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[1;32m    628\u001b[0m \n\u001b[1;32m    629\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    634\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    635\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 637\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPOST\u001b[39m\u001b[38;5;124m\"\u001b[39m, url, data\u001b[38;5;241m=\u001b[39mdata, json\u001b[38;5;241m=\u001b[39mjson, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    587\u001b[0m }\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(prep, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msend_kwargs)\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m adapter\u001b[38;5;241m.\u001b[39msend(request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/adapters.py:517\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    513\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m ProxyError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m    515\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _SSLError):\n\u001b[1;32m    516\u001b[0m         \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[0;32m--> 517\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m    519\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m    521\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[0;31mSSLError\u001b[0m: HTTPSConnectionPool(host='api.worldquantbrain.com', port=443): Max retries exceeded with url: /authentication (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))"]}], "source": ["s=login()\n", "len(get_operators(s))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# create a function to get details of a operator, return is json, url is https://api.worldquantbrain.com/datasetsoperators/{operator_id}. \n", "def get_operator_details(s, document_url):\n", "    response = s.get(f'https://api.worldquantbrain.com{document_url}')\n", "    operator = response.json()\n", "    return operator\n", "\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"YK49234\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"BRAIN_LABS\",\"BRAIN_LABS_JUPYTER_LAB\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"VISUALIZATION\",\"WORKDAY\"]}'\n"]}, {"ename": "JSONDecodeError", "evalue": "Expecting value: line 1 column 1 (char 0)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/models.py:971\u001b[0m, in \u001b[0;36mResponse.json\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    970\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 971\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m complex<PERSON>son\u001b[38;5;241m.\u001b[39mloads(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    972\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    973\u001b[0m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[1;32m    974\u001b[0m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplejson.JSONDecodeError\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/simplejson/__init__.py:514\u001b[0m, in \u001b[0;36mloads\u001b[0;34m(s, encoding, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, use_decimal, allow_nan, **kw)\u001b[0m\n\u001b[1;32m    510\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m encoding \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    511\u001b[0m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[1;32m    512\u001b[0m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    513\u001b[0m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m use_decimal \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m allow_nan \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[0;32m--> 514\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m _default_decoder\u001b[38;5;241m.\u001b[39mdecode(s)\n\u001b[1;32m    515\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/simplejson/decoder.py:386\u001b[0m, in \u001b[0;36mJSONDecoder.decode\u001b[0;34m(self, s, _w, _PY3)\u001b[0m\n\u001b[1;32m    385\u001b[0m     s \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mstr\u001b[39m(s, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencoding)\n\u001b[0;32m--> 386\u001b[0m obj, end \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mraw_decode(s)\n\u001b[1;32m    387\u001b[0m end \u001b[38;5;241m=\u001b[39m _w(s, end)\u001b[38;5;241m.\u001b[39mend()\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/simplejson/decoder.py:416\u001b[0m, in \u001b[0;36mJSONDecoder.raw_decode\u001b[0;34m(self, s, idx, _w, _PY3)\u001b[0m\n\u001b[1;32m    415\u001b[0m         idx \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m3\u001b[39m\n\u001b[0;32m--> 416\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscan_once(s, idx\u001b[38;5;241m=\u001b[39m_w(s, idx)\u001b[38;5;241m.\u001b[39mend())\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mJSONDecodeError\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[11], line 20\u001b[0m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m index, row \u001b[38;5;129;01min\u001b[39;00m df_operators\u001b[38;5;241m.\u001b[39miterrows():\n\u001b[1;32m     19\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mnotnull(row[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdocumentation\u001b[39m\u001b[38;5;124m'\u001b[39m]) \u001b[38;5;129;01mand\u001b[39;00m row[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdocumentation\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mNone\u001b[39m\u001b[38;5;124m'\u001b[39m:\n\u001b[0;32m---> 20\u001b[0m         detail_operator \u001b[38;5;241m=\u001b[39m get_operator_details(s, row[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdocumentation\u001b[39m\u001b[38;5;124m'\u001b[39m])\n\u001b[1;32m     21\u001b[0m         df_operators\u001b[38;5;241m.\u001b[39mat[index, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mdetails\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m detail_operator\n\u001b[1;32m     22\u001b[0m         df_operators\u001b[38;5;241m.\u001b[39mat[index, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m detail_operator\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcontent\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "Cell \u001b[0;32mIn[10], line 4\u001b[0m, in \u001b[0;36mget_operator_details\u001b[0;34m(s, document_url)\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget_operator_details\u001b[39m(s, document_url):\n\u001b[1;32m      3\u001b[0m     response \u001b[38;5;241m=\u001b[39m s\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://api.worldquantbrain.com\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdocument_url\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m----> 4\u001b[0m     operator \u001b[38;5;241m=\u001b[39m response\u001b[38;5;241m.\u001b[39mjson()\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m operator\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/requests/models.py:975\u001b[0m, in \u001b[0;36mResponse.json\u001b[0;34m(self, **kwargs)\u001b[0m\n\u001b[1;32m    971\u001b[0m     \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m <PERSON><PERSON><PERSON>\u001b[38;5;241m.\u001b[39mloads(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    972\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    973\u001b[0m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[1;32m    974\u001b[0m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplejson.JSONDecodeError\u001b[39;00m\n\u001b[0;32m--> 975\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RequestsJSONDecodeError(e\u001b[38;5;241m.\u001b[39mmsg, e\u001b[38;5;241m.\u001b[39mdoc, e\u001b[38;5;241m.\u001b[39mpos)\n", "\u001b[0;31mJSONDecodeError\u001b[0m: Expecting value: line 1 column 1 (char 0)"]}], "source": ["s=login()\n", "\n", "# Get all Operators, in list of dict\n", "list_of_operators = get_operators(s)\n", "\n", "# convert the list of dict to a pandas dataframe\n", "df_operators = pd.DataFrame(list_of_operators)\n", "\n", "# loop df_operator, \"documentation\" column is not None, get the details of the operator, convert to a dataframe using get_operator(s, document_url), then merge with df_operators. \n", "# all columns in the details should be added to df_operators\n", "\n", "# add 'details', 'lastModified', 'content' to df_operators\n", "\n", "df_operators['details'] = None\n", "df_operators['content'] = None\n", "df_operators['lastModified'] = None\n", "\n", "for index, row in df_operators.iterrows():\n", "    if pd.notnull(row['documentation']) and row['documentation'] != 'None':\n", "        detail_operator = get_operator_details(s, row['documentation'])\n", "        df_operators.at[index, 'details'] = detail_operator\n", "        df_operators.at[index, 'content'] = detail_operator.get('content')\n", "        df_operators.at[index, 'lastModified'] = detail_operator.get('lastModified')\n", "        \n", "\n", "df_operators.tail()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Save all the details of the operators to a csv file\n", "df_operators.to_csv('operators_2025-master.csv', index=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 抓Datafields\n", "\n", "多个region，settings的datasets"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# OPTIONS url = “https://api.worldquantbrain.com/simulations” 获得json格式返回值。\n", "def options_simulations(s):\n", "    response = s.options('https://api.worldquantbrain.com/simulations')\n", "    return response.json()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'{\"user\":{\"id\":\"YK49234\"},\"token\":{\"expiry\":14400.0},\"permissions\":[\"BEFORE_AND_AFTER_PERFORMANCE_V2\",\"BRAIN_LABS\",\"BRAIN_LABS_JUPYTER_LAB\",\"CONSULTANT\",\"MULTI_SIMULATION\",\"PROD_ALPHAS\",\"REFERRAL\",\"VISUALIZATION\",\"WORKDAY\"]}'\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP3000'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP1000'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP500'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOP200'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'USA', 'delay': 1, 'universe': 'TOPSP500'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP3000'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP1000'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP500'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOP200'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'USA', 'delay': 0, 'universe': 'TOPSP500'}\n", "{'region': 'GLB', 'delay': 1, 'universe': 'TOP3000'}\n", "{'region': 'GLB', 'delay': 1, 'universe': 'MINVOL1M'}\n", "{'region': 'GLB', 'delay': 1, 'universe': 'TOPDIV3000'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP2500'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP1200'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP800'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'TOP400'}\n", "{'region': 'EUR', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP2500'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP1200'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP800'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'TOP400'}\n", "{'region': 'EUR', 'delay': 0, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'ASI', 'delay': 1, 'universe': 'MINVOL1M'}\n", "{'region': 'ASI', 'delay': 1, 'universe': 'ILLIQUID_MINVOL1M'}\n", "{'region': 'CHN', 'delay': 0, 'universe': 'TOP2000U'}\n", "{'region': 'CHN', 'delay': 1, 'universe': 'TOP2000U'}\n"]}], "source": ["\n", "s=login()\n", "settings = options_simulations(s)\n", "settings\n", "\n", "# Function to get all combinations\n", "def get_combinations(settings):\n", "\n", "    config = settings['actions']['POST']['settings']['children']\n", "    regions = config['region']['choices']['instrumentType']['EQUITY']\n", "    delays = config['delay']['choices']['instrumentType']['EQUITY']\n", "    universes = config['universe']['choices']['instrumentType']['EQUITY']\n", "\n", "    combinations = []\n", "    for region in regions:\n", "        region_value = region['value']\n", "        region_delays = delays['region'].get(region_value, [])\n", "        region_universes = universes['region'].get(region_value, [])\n", "        \n", "        for delay in region_delays:\n", "            for universe in region_universes:\n", "                combinations.append({\n", "                    'region': region_value,\n", "                    'delay': delay['value'],\n", "                    'universe': universe['value']\n", "                })\n", "    return combinations\n", "\n", "# Get and print all combinations\n", "all_combinations = get_combinations(settings)\n", "for combo in all_combinations:\n", "    print(combo)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["\n", "from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type\n", "import requests\n", "@retry(\n", "    stop=stop_after_attempt(5),  # 增加重试次数到5次\n", "    wait=wait_exponential(multiplier=30, min=60, max=300),  # 调整等待时间\n", "    retry=retry_if_exception_type(requests.exceptions.RequestException)  # 只在特定异常时重试\n", ")\n", "def get_datafields(s, instrumentType, region, delay, universe):\n", "    timeout=180\n", "    try:\n", "        response = s.get(\n", "            f'https://api.worldquantbrain.com/data-fields?instrumentType=EQUITY&region={region}&delay={delay}&universe={universe}',\n", "            timeout=timeout\n", "        )\n", "        response.raise_for_status()  # Raises an HTTPError for bad responses\n", "        datafields = response.json()\n", "        return datafields\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"An error occurred: {e}\")\n", "        raise  # Re-raise the exception to trigger a retry\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datafields_USA_1_TOP3000.csv saved - 43006\n", "datafields_USA_1_TOP1000.csv saved - 43006\n", "datafields_USA_1_TOP500.csv saved - 43006\n", "datafields_USA_1_TOP200.csv saved - 43006\n", "datafields_USA_1_ILLIQUID_MINVOL1M.csv saved - 33070\n", "datafields_USA_1_TOPSP500.csv saved - 37450\n", "datafields_USA_0_TOP3000.csv saved - 8126\n", "datafields_USA_0_TOP1000.csv saved - 8126\n", "datafields_USA_0_TOP500.csv saved - 8126\n", "datafields_USA_0_TOP200.csv saved - 8126\n", "datafields_USA_0_ILLIQUID_MINVOL1M.csv saved - 5038\n", "datafields_USA_0_TOPSP500.csv saved - 6741\n", "datafields_GLB_1_TOP3000.csv saved - 11874\n", "datafields_GLB_1_MINVOL1M.csv saved - 11874\n", "datafields_GLB_1_TOPDIV3000.csv saved - 11874\n", "datafields_EUR_1_TOP2500.csv saved - 15712\n", "datafields_EUR_1_TOP1200.csv saved - 15712\n", "datafields_EUR_1_TOP800.csv saved - 15712\n", "datafields_EUR_1_TOP400.csv saved - 15712\n", "datafields_EUR_1_ILLIQUID_MINVOL1M.csv saved - 11443\n", "datafields_EUR_0_TOP2500.csv saved - 1958\n", "datafields_EUR_0_TOP1200.csv saved - 1958\n", "datafields_EUR_0_TOP800.csv saved - 1958\n", "datafields_EUR_0_TOP400.csv saved - 1958\n", "datafields_EUR_0_ILLIQUID_MINVOL1M.csv saved - 994\n", "datafields_ASI_1_MINVOL1M.csv saved - 7004\n", "datafields_ASI_1_ILLIQUID_MINVOL1M.csv saved - 2604\n", "datafields_CHN_0_TOP2000U.csv saved - 830\n", "datafields_CHN_1_TOP2000U.csv saved - 4782\n"]}], "source": ["ddf=pd.DataFrame()\n", "# loop all_combinations, get the datafields for each combination, then save to a csv file.\n", "for combo in all_combinations:\n", "    try:\n", "        datafields = get_datafields(s, 'EQUITY', combo['region'], combo['delay'], combo['universe'])\n", "        df_datafields = pd.json_normalize(datafields['results'])\n", "        df_datafields=pd.DataFrame(df_datafields)\n", "        ddf=pd.concat([ddf,df_datafields],axis=0)\n", "        # save to csv\n", "        #df_datafields.to_excel(f'datafields_{combo[\"region\"]}_{combo[\"delay\"]}_{combo[\"universe\"]}.xlsx')\n", "        print(f'datafields_{combo[\"region\"]}_{combo[\"delay\"]}_{combo[\"universe\"]}.csv saved - ' + str(len(df_datafields)))\n", "    except Exception as e:\n", "        print(f\"Failed to get datafields for {combo}: {e}\")\n", "ddf.to_csv('alldatausable.csv')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["ddf.to_csv('data.csv')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'user_package'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mpd\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01muser_package\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmachine_lib\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m login\n\u001b[1;32m      5\u001b[0m user_id \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      6\u001b[0m s \u001b[38;5;241m=\u001b[39m login()\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'user_package'"]}], "source": ["import pandas as pd\n", "\n", "from user_package.machine_lib import login\n", "\n", "user_id = \"\"\n", "s = login()\n", "res = s.get(\"https://api.worldquantbrain.com/operators\")\n", "df = pd.<PERSON><PERSON><PERSON><PERSON>(res.json())[['name']]\n", "df.to_csv(f\"{user_id}_operators.csv\", index=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 2}