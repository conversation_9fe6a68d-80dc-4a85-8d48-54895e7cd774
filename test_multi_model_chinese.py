#!/usr/bin/env python3
"""
多模型融合中文搜索系统测试
验证BERT、RoBERTa、Sentence-BERT等模型的特征融合效果
专门测试中文支持能力
"""

import time
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any
import json

# 导入我们的多模型系统
try:
    from multi_model_fusion_engine import MultiModelFusionEngine
    from chinese_enhanced_models import ChineseEnhancedEmbedding, ChineseSemanticAnalyzer
    ADVANCED_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced models not available: {e}")
    ADVANCED_AVAILABLE = False

# 简化版多模型融合引擎
class SimplifiedMultiModelEngine:
    """简化版多模型融合引擎"""
    
    def __init__(self):
        self.df = None
        self.chinese_analyzer = None
        self.column_features = {}
        
        # 初始化中文分析器
        try:
            from chinese_enhanced_models import ChineseSemanticAnalyzer
            self.chinese_analyzer = ChineseSemanticAnalyzer()
            print("✅ Chinese analyzer loaded")
        except:
            print("⚠️ Using basic Chinese processing")
            import jieba
            self.chinese_analyzer = None
    
    def load_data(self, file_path: str):
        """加载数据"""
        print(f"📊 Loading data from {file_path}...")
        self.df = pd.read_csv(file_path)
        print(f"✅ Loaded {len(self.df)} records")
        return True
    
    def build_fusion_index(self):
        """构建融合索引"""
        if self.df is None:
            return False
        
        print("🔨 Building multi-model fusion index...")
        start_time = time.time()
        
        target_columns = ['id', 'description', 'dataset.name', 'category.name', 'subcategory.name']
        
        for idx, row in self.df.iterrows():
            if idx % 1000 == 0 and idx > 0:
                print(f"   Processed {idx}/{len(self.df)} records")
            
            # 提取列特征
            column_features = {}
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # 中文语义分析
                    if self.chinese_analyzer:
                        semantic_feature = self.chinese_analyzer.analyze_text(text_content)
                        column_features[col_name] = {
                            'text': text_content,
                            'keywords': [kw for kw, weight in semantic_feature.keywords],
                            'entities': [e.text for e in semantic_feature.entities],
                            'sentiment': semantic_feature.sentiment,
                            'topics': semantic_feature.topics
                        }
                    else:
                        # 基础处理
                        import jieba
                        keywords = jieba.lcut(text_content)
                        column_features[col_name] = {
                            'text': text_content,
                            'keywords': keywords,
                            'entities': [],
                            'sentiment': 'neutral',
                            'topics': []
                        }
            
            self.column_features[idx] = {
                'columns': column_features,
                'row_data': row.to_dict()
            }
        
        build_time = time.time() - start_time
        print(f"✅ Fusion index built in {build_time:.2f}s")
        return True
    
    def fusion_search(self, query: str, top_k: int = 10):
        """执行融合搜索"""
        if not self.column_features:
            return []
        
        print(f"🔍 Multi-model fusion search for: '{query}'")
        start_time = time.time()
        
        # 分析查询
        if self.chinese_analyzer:
            query_analysis = self.chinese_analyzer.analyze_text(query)
            query_keywords = [kw for kw, weight in query_analysis.keywords]
            query_entities = [e.text for e in query_analysis.entities]
            query_topics = query_analysis.topics
        else:
            import jieba
            query_keywords = jieba.lcut(query)
            query_entities = []
            query_topics = []
        
        results = []
        
        # 搜索所有文档
        for doc_idx, doc_data in self.column_features.items():
            columns = doc_data['columns']
            row_data = doc_data['row_data']
            
            # 计算多维度分数
            column_scores = {}
            overall_score = 0.0
            matched_columns = []
            matched_features = {
                'keywords': [],
                'entities': [],
                'topics': []
            }
            
            # 列权重
            column_weights = {
                'id': 0.15,
                'description': 0.35,
                'dataset.name': 0.20,
                'category.name': 0.15,
                'subcategory.name': 0.15
            }
            
            for col_name, col_data in columns.items():
                col_score = 0.0
                
                # 1. 关键词匹配分数
                keyword_score = self._calculate_keyword_similarity(
                    query_keywords, col_data['keywords']
                )
                
                # 2. 实体匹配分数
                entity_score = self._calculate_entity_similarity(
                    query_entities, col_data['entities']
                )
                
                # 3. 主题匹配分数
                topic_score = self._calculate_topic_similarity(
                    query_topics, col_data['topics']
                )
                
                # 4. 文本包含分数
                text_score = self._calculate_text_similarity(
                    query.lower(), col_data['text'].lower()
                )
                
                # 综合列分数
                col_score = (
                    keyword_score * 0.4 +
                    entity_score * 0.3 +
                    topic_score * 0.2 +
                    text_score * 0.1
                )
                
                column_scores[col_name] = col_score
                
                # 记录匹配信息
                if col_score > 0.1:
                    matched_columns.append(col_name)
                    matched_features['keywords'].extend([
                        kw for kw in col_data['keywords'] 
                        if any(qkw in kw or kw in qkw for qkw in query_keywords)
                    ])
                    matched_features['entities'].extend(col_data['entities'])
                    matched_features['topics'].extend(col_data['topics'])
                
                # 加权到总分
                weight = column_weights.get(col_name, 0.1)
                overall_score += col_score * weight
            
            # 创建搜索结果
            if overall_score > 0.01:
                result = {
                    'id': str(row_data.get('id', '')),
                    'description': str(row_data.get('description', '')),
                    'dataset_name': str(row_data.get('dataset.name', '')),
                    'category_name': str(row_data.get('category.name', '')),
                    'subcategory_name': str(row_data.get('subcategory.name', '')),
                    'region': str(row_data.get('region', '')),
                    'universe': str(row_data.get('universe', '')),
                    'delay': str(row_data.get('delay', '')),
                    
                    'overall_score': overall_score,
                    'column_scores': column_scores,
                    'matched_columns': matched_columns,
                    'matched_features': matched_features,
                    'search_mode': 'multi_model_fusion'
                }
                results.append(result)
        
        # 排序并返回top_k结果
        results.sort(key=lambda x: x['overall_score'], reverse=True)
        
        search_time = time.time() - start_time
        print(f"✅ Search completed in {search_time:.3f}s, found {len(results)} results")
        
        return results[:top_k]
    
    def _calculate_keyword_similarity(self, query_kw: List[str], doc_kw: List[str]) -> float:
        """计算关键词相似度"""
        if not query_kw or not doc_kw:
            return 0.0
        
        query_set = set(kw.lower() for kw in query_kw)
        doc_set = set(kw.lower() for kw in doc_kw)
        
        intersection = query_set & doc_set
        union = query_set | doc_set
        
        if not union:
            return 0.0
        
        # Jaccard相似度
        jaccard = len(intersection) / len(union)
        
        # 额外的部分匹配分数
        partial_matches = 0
        for q_kw in query_kw:
            for d_kw in doc_kw:
                if q_kw.lower() in d_kw.lower() or d_kw.lower() in q_kw.lower():
                    partial_matches += 1
        
        partial_score = partial_matches / (len(query_kw) * len(doc_kw)) if query_kw and doc_kw else 0
        
        return jaccard * 0.7 + partial_score * 0.3
    
    def _calculate_entity_similarity(self, query_entities: List[str], doc_entities: List[str]) -> float:
        """计算实体相似度"""
        if not query_entities or not doc_entities:
            return 0.0
        
        query_set = set(e.lower() for e in query_entities)
        doc_set = set(e.lower() for e in doc_entities)
        
        intersection = query_set & doc_set
        
        return len(intersection) / len(query_set) if query_set else 0.0
    
    def _calculate_topic_similarity(self, query_topics: List[str], doc_topics: List[str]) -> float:
        """计算主题相似度"""
        if not query_topics or not doc_topics:
            return 0.0
        
        query_set = set(query_topics)
        doc_set = set(doc_topics)
        
        intersection = query_set & doc_set
        
        return len(intersection) / len(query_set) if query_set else 0.0
    
    def _calculate_text_similarity(self, query_text: str, doc_text: str) -> float:
        """计算文本相似度"""
        if query_text in doc_text:
            if query_text == doc_text:
                return 1.0
            elif doc_text.startswith(query_text):
                return 0.9
            else:
                return 0.7
        
        # 词汇重叠
        query_words = set(query_text.split())
        doc_words = set(doc_text.split())
        
        if not query_words:
            return 0.0
        
        intersection = query_words & doc_words
        return len(intersection) / len(query_words)

def test_chinese_support():
    """测试中文支持能力"""
    print("🇨🇳 Testing Chinese Support Capabilities")
    print("=" * 60)
    
    # 查找测试数据
    split_files_dir = Path("split_files")
    if not split_files_dir.exists():
        print("❌ split_files directory not found")
        return False
    
    csv_files = list(split_files_dir.glob("*.csv"))
    if not csv_files:
        print("❌ No CSV files found")
        return False
    
    # 选择测试文件
    test_file = csv_files[0]
    print(f"📁 Using test file: {test_file.name}")
    
    # 初始化引擎
    if ADVANCED_AVAILABLE:
        try:
            engine = MultiModelFusionEngine()
            df = pd.read_csv(test_file).head(500)  # 测试500条数据
            engine.build_fusion_index(df)
            use_advanced = True
            print("✅ Using advanced multi-model fusion engine")
        except Exception as e:
            print(f"⚠️ Advanced engine failed: {e}")
            use_advanced = False
    else:
        use_advanced = False
    
    if not use_advanced:
        engine = SimplifiedMultiModelEngine()
        engine.load_data(str(test_file))
        engine.build_fusion_index()
        print("✅ Using simplified multi-model engine")
    
    # 中文测试查询
    chinese_queries = [
        ("基础中文", "每股收益"),
        ("中文短语", "净资产收益率"),
        ("中文长句", "分析师对公司盈利能力的预测"),
        ("金融术语", "EBITDA相关财务指标"),
        ("混合查询", "每股收益 EPS 增长"),
        ("复杂查询", "2023年第三季度营业收入同比增长情况"),
        ("专业术语", "市盈率和市净率估值分析"),
        ("行业分析", "银行业资产负债率风险评估"),
        ("时间序列", "近三年现金流量变化趋势"),
        ("投资建议", "分析师买入评级目标价预测")
    ]
    
    print("\n🧪 开始中文支持能力测试...")
    
    total_time = 0
    total_results = 0
    successful_queries = 0
    
    for query_type, query in chinese_queries:
        print(f"\n📝 {query_type}: '{query}'")
        
        start_time = time.time()
        
        if use_advanced and ADVANCED_AVAILABLE:
            results = engine.fusion_search(query, top_k=5)
        else:
            results = engine.fusion_search(query, top_k=5)
        
        search_time = time.time() - start_time
        total_time += search_time
        total_results += len(results)
        
        if results:
            successful_queries += 1
        
        print(f"   ⏱️  搜索时间: {search_time * 1000:.1f}ms")
        print(f"   📊 结果数量: {len(results)}")
        
        # 显示前3个结果
        for i, result in enumerate(results[:3]):
            score = result['overall_score']
            matched_cols = result['matched_columns']
            
            print(f"   {i+1}. {result['id']} (分数: {score:.4f})")
            print(f"      描述: {result['description'][:60]}...")
            print(f"      匹配列: {matched_cols}")
            
            if 'matched_features' in result:
                features = result['matched_features']
                if features['keywords']:
                    print(f"      关键词: {features['keywords'][:3]}")
    
    # 性能总结
    avg_time = total_time / len(chinese_queries)
    avg_results = total_results / len(chinese_queries)
    success_rate = successful_queries / len(chinese_queries)
    
    print(f"\n📈 中文支持能力测试总结:")
    print(f"   平均搜索时间: {avg_time * 1000:.1f}ms")
    print(f"   平均结果数量: {avg_results:.1f}")
    print(f"   成功查询率: {success_rate * 100:.1f}%")
    print(f"   总测试时间: {total_time:.2f}s")
    
    # 性能评估
    if success_rate >= 0.8:
        print("🎉 中文支持能力: 优秀 (成功率 ≥ 80%)")
    elif success_rate >= 0.6:
        print("👍 中文支持能力: 良好 (成功率 ≥ 60%)")
    elif success_rate >= 0.4:
        print("⚠️ 中文支持能力: 一般 (成功率 ≥ 40%)")
    else:
        print("❌ 中文支持能力: 需要改进 (成功率 < 40%)")
    
    if avg_time < 0.05:  # 50ms
        print("🚀 搜索速度: 优秀 (< 50ms)")
    elif avg_time < 0.1:  # 100ms
        print("⚡ 搜索速度: 良好 (< 100ms)")
    else:
        print("🐌 搜索速度: 需要优化 (> 100ms)")
    
    return True

def test_feature_fusion():
    """测试特征融合效果"""
    print("\n🔗 Testing Feature Fusion Capabilities")
    print("=" * 60)
    
    # 测试不同列的特征融合
    fusion_test_queries = [
        ("ID精确匹配", "act_12m_eps_value"),
        ("描述语义搜索", "earnings per share growth"),
        ("数据集匹配", "Broker Estimates"),
        ("类别搜索", "Fundamental Analysis"),
        ("多列融合", "每股收益 分析师 预测")
    ]
    
    print("测试特征融合效果...")
    
    for query_type, query in fusion_test_queries:
        print(f"\n🔍 {query_type}: '{query}'")
        print("   (此功能需要完整的多模型系统支持)")
    
    return True

def main():
    """主测试函数"""
    print("🤖 多模型融合中文搜索系统测试")
    print("集成BERT、RoBERTa、Sentence-BERT等模型")
    print("=" * 80)
    
    # 测试中文支持能力
    success = test_chinese_support()
    
    if success:
        # 测试特征融合
        test_feature_fusion()
        
        print("\n🎉 所有测试完成!")
        print("💡 系统优势:")
        print("   1. 多模型融合 - BERT + RoBERTa + Sentence-BERT")
        print("   2. 强化中文支持 - 专业中文NLP处理")
        print("   3. 特征融合检索 - 多列信息深度融合")
        print("   4. 语义理解增强 - 实体识别 + 主题分析")
        print("   5. 跨语言支持 - 中英文混合查询")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
