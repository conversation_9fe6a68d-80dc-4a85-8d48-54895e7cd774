#!/usr/bin/env python3
"""
测试高性能搜索系统
验证新架构的性能和准确性
"""

import time
import pandas as pd
from pathlib import Path
import asyncio
from typing import List, Dict
from loguru import logger

# 导入搜索引擎（使用简化版本避免依赖问题）
try:
    from advanced_search_engine import AdvancedSearchEngine, SearchMode
    from enhanced_chinese_processor import EnhancedChineseProcessor
    ADVANCED_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Advanced modules not available: {e}")
    ADVANCED_AVAILABLE = False

# 简化版搜索引擎（作为备选）
class SimpleAdvancedSearch:
    """简化版高性能搜索引擎"""
    
    def __init__(self):
        self.df = None
        self.text_processor = None
        logger.info("Initialized SimpleAdvancedSearch")
    
    def load_data(self, file_path: str):
        """加载数据"""
        logger.info(f"Loading data from {file_path}")
        self.df = pd.read_csv(file_path)
        logger.success(f"Loaded {len(self.df)} records")
        
        # 初始化文本处理器
        if ADVANCED_AVAILABLE:
            self.text_processor = EnhancedChineseProcessor()
        
        return True
    
    def build_index(self):
        """构建索引（简化版）"""
        if self.df is None:
            return False
        
        logger.info("Building search index...")
        
        # 创建搜索文本
        search_fields = ['id', 'description', 'dataset.name', 'category.name', 'subcategory.name']
        
        def create_searchable_text(row):
            texts = []
            for field in search_fields:
                if field in row and pd.notna(row[field]):
                    texts.append(str(row[field]))
            return " | ".join(texts)
        
        self.df['searchable_text'] = self.df.apply(create_searchable_text, axis=1)
        logger.success("Index built successfully")
        return True
    
    def search(self, query: str, top_k: int = 10, mode: str = "hybrid"):
        """执行搜索"""
        if self.df is None:
            return []
        
        start_time = time.time()
        
        # 简单的文本匹配搜索
        query_lower = query.lower()
        
        # 计算匹配分数
        def calculate_score(text):
            if pd.isna(text):
                return 0
            text_lower = str(text).lower()
            
            # 精确匹配
            if query_lower in text_lower:
                if query_lower == text_lower:
                    return 100
                elif text_lower.startswith(query_lower):
                    return 90
                else:
                    return 70
            
            # 词汇匹配
            query_words = query_lower.split()
            text_words = text_lower.split()
            
            matches = sum(1 for word in query_words if word in text_words)
            if matches > 0:
                return (matches / len(query_words)) * 50
            
            return 0
        
        # 搜索所有行
        results = []
        for idx, row in self.df.iterrows():
            max_score = 0
            
            # 检查各个字段
            for field in ['id', 'description', 'dataset.name', 'category.name', 'subcategory.name']:
                if field in row:
                    score = calculate_score(row[field])
                    max_score = max(max_score, score)
            
            if max_score > 0:
                results.append({
                    'id': row['id'],
                    'description': row.get('description', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'score': max_score / 100.0,
                    'search_mode': mode
                })
        
        # 排序并返回top_k
        results.sort(key=lambda x: x['score'], reverse=True)
        
        search_time = time.time() - start_time
        logger.info(f"Search completed in {search_time:.3f}s, found {len(results)} results")
        
        return results[:top_k]

def test_single_table_search():
    """测试单表搜索性能"""
    logger.info("🧪 Testing single table search performance...")
    
    # 查找可用的数据文件
    split_files_dir = Path("split_files")
    if not split_files_dir.exists():
        logger.error("split_files directory not found")
        return False
    
    csv_files = list(split_files_dir.glob("*.csv"))
    if not csv_files:
        logger.error("No CSV files found in split_files directory")
        return False
    
    # 选择一个较大的文件进行测试
    test_file = None
    max_size = 0
    
    for file_path in csv_files:
        try:
            df = pd.read_csv(file_path, nrows=1)  # 只读取第一行来检查文件
            file_size = file_path.stat().st_size
            if file_size > max_size:
                max_size = file_size
                test_file = file_path
        except:
            continue
    
    if not test_file:
        logger.error("No valid CSV files found")
        return False
    
    logger.info(f"Selected test file: {test_file.name} ({max_size / 1024 / 1024:.1f}MB)")
    
    # 初始化搜索引擎
    if ADVANCED_AVAILABLE:
        try:
            engine = AdvancedSearchEngine()
            df = pd.read_csv(test_file)
            logger.info(f"Building advanced index for {len(df)} records...")
            engine.build_index(df)
            use_advanced = True
        except Exception as e:
            logger.warning(f"Advanced engine failed: {e}, falling back to simple engine")
            use_advanced = False
    else:
        use_advanced = False
    
    if not use_advanced:
        engine = SimpleAdvancedSearch()
        engine.load_data(str(test_file))
        engine.build_index()
    
    # 测试查询
    test_queries = [
        ("英文专业术语", "earnings per share"),
        ("英文缩写", "EBITDA"),
        ("中文查询", "每股收益"),
        ("混合查询", "分析师 analyst"),
        ("ID模式", "act_12m_eps"),
        ("数据集查询", "Broker Estimates"),
        ("类别查询", "Fundamental"),
        ("长查询", "净资产收益率相关财务指标")
    ]
    
    logger.info("🔍 Starting search performance tests...")
    
    total_time = 0
    total_results = 0
    
    for query_type, query in test_queries:
        logger.info(f"\n📝 测试查询: {query_type} - '{query}'")
        
        start_time = time.time()
        
        if use_advanced and ADVANCED_AVAILABLE:
            results = engine.search(query, mode=SearchMode.HYBRID, top_k=10)
            # 转换结果格式
            formatted_results = []
            for result in results:
                formatted_results.append({
                    'id': result.id,
                    'description': result.description,
                    'score': result.score,
                    'search_mode': result.search_mode
                })
            results = formatted_results
        else:
            results = engine.search(query, top_k=10)
        
        search_time = time.time() - start_time
        total_time += search_time
        total_results += len(results)
        
        logger.info(f"   ⏱️  搜索时间: {search_time * 1000:.1f}ms")
        logger.info(f"   📊 结果数量: {len(results)}")
        
        # 显示前3个结果
        for i, result in enumerate(results[:3]):
            logger.info(f"   {i+1}. {result['id']} (分数: {result['score']:.4f})")
            if len(result['description']) > 80:
                desc = result['description'][:80] + "..."
            else:
                desc = result['description']
            logger.info(f"      {desc}")
    
    # 性能总结
    avg_time = total_time / len(test_queries)
    avg_results = total_results / len(test_queries)
    
    logger.success(f"\n📈 性能测试总结:")
    logger.success(f"   平均搜索时间: {avg_time * 1000:.1f}ms")
    logger.success(f"   平均结果数量: {avg_results:.1f}")
    logger.success(f"   总测试时间: {total_time:.2f}s")
    logger.success(f"   使用引擎: {'Advanced' if use_advanced else 'Simple'}")
    
    # 性能评估
    if avg_time < 0.1:  # 100ms
        logger.success("🚀 搜索速度: 优秀 (< 100ms)")
    elif avg_time < 0.5:  # 500ms
        logger.info("⚡ 搜索速度: 良好 (< 500ms)")
    elif avg_time < 2.0:  # 2s
        logger.warning("🐌 搜索速度: 一般 (< 2s)")
    else:
        logger.error("🐢 搜索速度: 需要优化 (> 2s)")
    
    return True

def test_chinese_processing():
    """测试中文处理能力"""
    if not ADVANCED_AVAILABLE:
        logger.warning("Advanced Chinese processing not available")
        return
    
    logger.info("🇨🇳 Testing Chinese text processing...")
    
    processor = EnhancedChineseProcessor()
    
    test_texts = [
        "每股收益增长率超过15%",
        "2023年第三季度净资产收益率",
        "分析师对EBITDA的最新预测",
        "市盈率低于20倍的优质股票",
        "营业收入同比增长30%以上"
    ]
    
    for text in test_texts:
        logger.info(f"\n📝 处理文本: '{text}'")
        
        # 文本处理
        processed = processor.process_text(text)
        logger.info(f"   分词结果: {processed.tokens}")
        logger.info(f"   关键词: {processed.keywords}")
        
        # 意图分析
        intent = processor.analyze_query_intent(text)
        logger.info(f"   查询类型: {intent['query_type']}")
        logger.info(f"   金融术语: {intent['financial_terms']}")
        logger.info(f"   扩展查询: {intent['expanded_query'][:3]}...")

def main():
    """主测试函数"""
    logger.info("🔍 高性能中文搜索系统测试")
    logger.info("=" * 60)
    
    # 测试单表搜索
    success = test_single_table_search()
    
    if success:
        # 测试中文处理
        test_chinese_processing()
        
        logger.success("\n🎉 所有测试完成!")
        logger.info("💡 建议:")
        logger.info("   1. 使用 python fast_search_api.py 启动高性能API服务")
        logger.info("   2. 访问 http://localhost:8000 使用Web界面")
        logger.info("   3. 查看 http://localhost:8000/docs 获取API文档")
    else:
        logger.error("❌ 测试失败")

if __name__ == "__main__":
    main()
