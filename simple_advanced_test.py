#!/usr/bin/env python3
"""
简化版高性能搜索系统测试
不依赖额外包，专注于核心功能验证
"""

import time
import pandas as pd
import numpy as np
from pathlib import Path
import jieba
import re
from typing import List, Dict, Any
from collections import defaultdict

class OptimizedChineseProcessor:
    """优化的中文文本处理器"""
    
    def __init__(self):
        # 金融术语词典
        self.financial_terms = {
            '每股收益', 'EPS', '净资产收益率', 'ROE', '总资产收益率', 'ROA',
            '毛利率', '净利率', '营业利润率', '资产负债率', '流动比率',
            '市盈率', 'PE', '市净率', 'PB', 'EBITDA', '营业收入', '净利润',
            '分析师', '预测', '估算', '评级', '目标价', '买入', '卖出', '持有'
        }
        
        # 停用词
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都',
            '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着'
        }
        
        # 同义词词典
        self.synonyms = {
            '每股收益': ['EPS', '每股盈利'],
            '净资产收益率': ['ROE', '股东权益回报率'],
            '市盈率': ['PE', 'P/E', '价格收益比'],
            '分析师': ['研究员', '分析员']
        }
        
        # 添加金融术语到jieba词典
        for term in self.financial_terms:
            jieba.add_word(term, freq=1000)
    
    def clean_text(self, text: str) -> str:
        """清洗文本"""
        if not text or pd.isna(text):
            return ""
        
        text = str(text)
        # 移除特殊字符但保留中文、英文、数字
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-_.,()%/]', ' ', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def segment_text(self, text: str) -> List[str]:
        """中文分词"""
        if not text:
            return []
        
        words = jieba.lcut(text)
        filtered_words = []
        
        for word in words:
            word = word.strip()
            if (len(word) >= 2 and 
                not word.isdigit() and 
                word not in self.stop_words):
                filtered_words.append(word)
        
        return filtered_words
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        words = self.segment_text(text)
        
        # 计算词权重
        word_weights = defaultdict(float)
        for word in words:
            weight = 1.0
            
            # 金融术语权重更高
            if word in self.financial_terms:
                weight *= 3.0
            
            # 长词权重更高
            if len(word) >= 3:
                weight *= 1.5
            
            word_weights[word] += weight
        
        # 排序并返回top_k
        sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
        return [word for word, weight in sorted_words[:top_k]]
    
    def expand_query(self, query: str) -> List[str]:
        """查询扩展"""
        expanded = [query]
        words = self.segment_text(query)
        
        for word in words:
            if word in self.synonyms:
                expanded.extend(self.synonyms[word])
        
        return list(set(expanded))

class HighPerformanceSearchEngine:
    """高性能搜索引擎"""
    
    def __init__(self):
        self.df = None
        self.text_processor = OptimizedChineseProcessor()
        self.search_index = {}
        self.keyword_index = defaultdict(set)
        
    def load_data(self, file_path: str) -> bool:
        """加载数据"""
        try:
            print(f"📊 Loading data from {file_path}...")
            self.df = pd.read_csv(file_path)
            print(f"✅ Loaded {len(self.df)} records")
            return True
        except Exception as e:
            print(f"❌ Failed to load data: {e}")
            return False
    
    def build_index(self) -> bool:
        """构建搜索索引"""
        if self.df is None:
            return False
        
        print("🔨 Building search index...")
        start_time = time.time()
        
        # 创建搜索文本
        search_fields = ['id', 'description', 'dataset.name', 'category.name', 'subcategory.name']
        
        def create_searchable_text(row):
            texts = []
            for field in search_fields:
                if field in row and pd.notna(row[field]):
                    cleaned = self.text_processor.clean_text(row[field])
                    if cleaned:
                        texts.append(cleaned)
            return " | ".join(texts)
        
        # 处理每一行
        for idx, row in self.df.iterrows():
            searchable_text = create_searchable_text(row)
            
            # 存储搜索文本
            self.search_index[idx] = {
                'text': searchable_text,
                'keywords': self.text_processor.extract_keywords(searchable_text),
                'row_data': row.to_dict()
            }
            
            # 构建关键词倒排索引
            keywords = self.text_processor.segment_text(searchable_text)
            for keyword in keywords:
                self.keyword_index[keyword.lower()].add(idx)
        
        build_time = time.time() - start_time
        print(f"✅ Index built in {build_time:.2f}s")
        return True
    
    def keyword_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """关键词搜索"""
        query_words = self.text_processor.segment_text(query)
        query_words_lower = [word.lower() for word in query_words]
        
        # 计算文档分数
        doc_scores = defaultdict(float)
        
        for word in query_words_lower:
            if word in self.keyword_index:
                # TF-IDF简化版本
                df = len(self.keyword_index[word])  # 文档频率
                idf = np.log(len(self.search_index) / (df + 1))
                
                for doc_id in self.keyword_index[word]:
                    # 计算词频
                    doc_keywords = [kw.lower() for kw in self.search_index[doc_id]['keywords']]
                    tf = doc_keywords.count(word)
                    doc_scores[doc_id] += tf * idf
        
        # 排序并返回结果
        sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)
        
        results = []
        for doc_id, score in sorted_docs[:top_k]:
            row_data = self.search_index[doc_id]['row_data']
            results.append({
                'id': row_data.get('id', ''),
                'description': row_data.get('description', ''),
                'dataset_name': row_data.get('dataset.name', ''),
                'category_name': row_data.get('category.name', ''),
                'subcategory_name': row_data.get('subcategory.name', ''),
                'score': score,
                'search_mode': 'keyword'
            })
        
        return results
    
    def fuzzy_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """模糊搜索"""
        query_lower = query.lower()
        results = []
        
        for doc_id, doc_data in self.search_index.items():
            text_lower = doc_data['text'].lower()
            
            # 计算匹配分数
            score = 0
            
            # 完全匹配
            if query_lower in text_lower:
                if query_lower == text_lower:
                    score = 100
                elif text_lower.startswith(query_lower):
                    score = 90
                else:
                    score = 70
            else:
                # 词汇匹配
                query_words = query_lower.split()
                text_words = text_lower.split()
                
                matches = sum(1 for word in query_words if word in text_words)
                if matches > 0:
                    score = (matches / len(query_words)) * 50
            
            if score > 0:
                row_data = doc_data['row_data']
                results.append({
                    'id': row_data.get('id', ''),
                    'description': row_data.get('description', ''),
                    'dataset_name': row_data.get('dataset.name', ''),
                    'category_name': row_data.get('category.name', ''),
                    'subcategory_name': row_data.get('subcategory.name', ''),
                    'score': score / 100.0,
                    'search_mode': 'fuzzy'
                })
        
        # 排序并返回top_k
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:top_k]
    
    def hybrid_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """混合搜索"""
        # 执行多种搜索
        keyword_results = self.keyword_search(query, top_k * 2)
        fuzzy_results = self.fuzzy_search(query, top_k * 2)
        
        # 合并结果
        combined_results = {}
        
        # 添加关键词搜索结果
        for result in keyword_results:
            item_id = result['id']
            if item_id not in combined_results:
                result['combined_score'] = result['score'] * 0.6
                result['methods'] = ['keyword']
                combined_results[item_id] = result
            else:
                combined_results[item_id]['combined_score'] += result['score'] * 0.6
                combined_results[item_id]['methods'].append('keyword')
        
        # 添加模糊搜索结果
        for result in fuzzy_results:
            item_id = result['id']
            if item_id not in combined_results:
                result['combined_score'] = result['score'] * 0.4
                result['methods'] = ['fuzzy']
                combined_results[item_id] = result
            else:
                combined_results[item_id]['combined_score'] += result['score'] * 0.4
                combined_results[item_id]['methods'].append('fuzzy')
        
        # 排序并返回结果
        final_results = list(combined_results.values())
        final_results.sort(key=lambda x: x['combined_score'], reverse=True)
        
        for i, result in enumerate(final_results[:top_k]):
            result['rank'] = i + 1
            result['search_mode'] = 'hybrid'
        
        return final_results[:top_k]

def test_performance():
    """测试搜索性能"""
    print("🔍 高性能中文搜索系统测试")
    print("=" * 60)
    
    # 查找测试数据文件
    split_files_dir = Path("split_files")
    if not split_files_dir.exists():
        print("❌ split_files directory not found")
        return False
    
    csv_files = list(split_files_dir.glob("*.csv"))
    if not csv_files:
        print("❌ No CSV files found")
        return False
    
    # 选择一个文件进行测试
    test_file = csv_files[0]  # 使用第一个文件
    print(f"📁 Using test file: {test_file.name}")
    
    # 初始化搜索引擎
    engine = HighPerformanceSearchEngine()
    
    if not engine.load_data(str(test_file)):
        return False
    
    if not engine.build_index():
        return False
    
    # 测试查询
    test_queries = [
        ("英文专业术语", "earnings per share"),
        ("英文缩写", "EBITDA"),
        ("中文查询", "每股收益"),
        ("混合查询", "分析师 analyst"),
        ("ID查询", "act_12m"),
        ("长查询", "净资产收益率相关指标")
    ]
    
    print("\n🧪 开始性能测试...")
    
    total_time = 0
    total_results = 0
    
    for query_type, query in test_queries:
        print(f"\n📝 {query_type}: '{query}'")
        
        # 测试混合搜索
        start_time = time.time()
        results = engine.hybrid_search(query, top_k=5)
        search_time = time.time() - start_time
        
        total_time += search_time
        total_results += len(results)
        
        print(f"   ⏱️  搜索时间: {search_time * 1000:.1f}ms")
        print(f"   📊 结果数量: {len(results)}")
        
        # 显示前3个结果
        for i, result in enumerate(results[:3]):
            score = result.get('combined_score', result.get('score', 0))
            methods = result.get('methods', [result.get('search_mode', 'unknown')])
            print(f"   {i+1}. {result['id']} (分数: {score:.4f}, 方法: {methods})")
            
            desc = result['description']
            if len(desc) > 60:
                desc = desc[:60] + "..."
            print(f"      {desc}")
    
    # 性能总结
    avg_time = total_time / len(test_queries)
    avg_results = total_results / len(test_queries)
    
    print(f"\n📈 性能测试总结:")
    print(f"   平均搜索时间: {avg_time * 1000:.1f}ms")
    print(f"   平均结果数量: {avg_results:.1f}")
    print(f"   总测试时间: {total_time:.2f}s")
    
    # 性能评估
    if avg_time < 0.01:  # 10ms
        print("🚀 搜索速度: 优秀 (< 10ms)")
    elif avg_time < 0.1:  # 100ms
        print("⚡ 搜索速度: 良好 (< 100ms)")
    elif avg_time < 0.5:  # 500ms
        print("🐌 搜索速度: 一般 (< 500ms)")
    else:
        print("🐢 搜索速度: 需要优化 (> 500ms)")
    
    print(f"\n🎯 相比原系统性能提升:")
    original_time = 45.0  # 原系统平均45秒
    improvement = original_time / avg_time
    print(f"   速度提升: {improvement:.0f}倍")
    print(f"   从 {original_time:.1f}s 优化到 {avg_time * 1000:.1f}ms")
    
    return True

if __name__ == "__main__":
    success = test_performance()
    
    if success:
        print("\n🎉 测试完成!")
        print("💡 建议:")
        print("   1. 使用更强大的向量模型可进一步提升效果")
        print("   2. 部署到生产环境时考虑使用Redis缓存")
        print("   3. 可以根据业务需求调整搜索权重")
    else:
        print("❌ 测试失败")
