#!/usr/bin/env python3
"""
中文精准模糊搜索系统演示脚本
展示各种搜索功能和使用方法
"""

import time
from simple_test import SimpleSearchEngine

def print_separator(title=""):
    """打印分隔符"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)

def print_results(results, max_results=5):
    """格式化打印搜索结果"""
    if not results:
        print("❌ 未找到相关结果")
        return
    
    print(f"✅ 找到 {len(results)} 个结果:")
    for i, result in enumerate(results[:max_results]):
        print(f"\n{result['rank']}. 【{result['id']}】")
        print(f"   📝 描述: {result['description'][:100]}...")
        print(f"   📊 数据集: {result['dataset_name']}")
        print(f"   📂 类别: {result['category_name']} > {result['subcategory_name']}")
        print(f"   🎯 分数: {result.get('combined_score', result.get('score', 0)):.2f}")
        if 'methods' in result:
            print(f"   🔍 搜索方法: {', '.join(result['methods'])}")

def demo_search_types(engine):
    """演示不同类型的搜索"""
    print_separator("搜索类型演示")
    
    query = "earnings per share"
    print(f"🔍 查询: '{query}'")
    
    # 精确搜索
    print(f"\n1️⃣ 精确搜索:")
    start_time = time.time()
    exact_results = engine.exact_search(query, top_k=3)
    exact_time = time.time() - start_time
    print(f"   ⏱️ 耗时: {exact_time:.2f}秒")
    print_results(exact_results, 3)
    
    # 模糊搜索
    print(f"\n2️⃣ 模糊搜索:")
    start_time = time.time()
    fuzzy_results = engine.fuzzy_search(query, top_k=3)
    fuzzy_time = time.time() - start_time
    print(f"   ⏱️ 耗时: {fuzzy_time:.2f}秒")
    print_results(fuzzy_results, 3)
    
    # TF-IDF搜索
    print(f"\n3️⃣ TF-IDF搜索:")
    start_time = time.time()
    tfidf_results = engine.tfidf_search(query, top_k=3)
    tfidf_time = time.time() - start_time
    print(f"   ⏱️ 耗时: {tfidf_time:.2f}秒")
    print_results(tfidf_results, 3)
    
    # 混合搜索
    print(f"\n4️⃣ 混合搜索:")
    start_time = time.time()
    hybrid_results = engine.hybrid_search(query, top_k=3)
    hybrid_time = time.time() - start_time
    print(f"   ⏱️ 耗时: {hybrid_time:.2f}秒")
    print_results(hybrid_results, 3)

def demo_multilingual_search(engine):
    """演示多语言搜索"""
    print_separator("多语言搜索演示")
    
    test_queries = [
        ("英文专业术语", "EBITDA"),
        ("英文短语", "cash flow"),
        ("中文词汇", "分析师"),
        ("混合查询", "每股收益 EPS"),
        ("ID查询", "act_12m_eps_value")
    ]
    
    for query_type, query in test_queries:
        print(f"\n🌐 {query_type}: '{query}'")
        start_time = time.time()
        results = engine.hybrid_search(query, top_k=3)
        search_time = time.time() - start_time
        print(f"   ⏱️ 搜索时间: {search_time:.2f}秒")
        print_results(results, 2)

def demo_field_analysis(engine):
    """演示字段分析"""
    print_separator("数据字段分析")
    
    # 分析数据集分布
    dataset_counts = engine.df['dataset.name'].value_counts().head(10)
    print("📊 Top 10 数据集:")
    for dataset, count in dataset_counts.items():
        print(f"   • {dataset}: {count} 条记录")
    
    # 分析类别分布
    category_counts = engine.df['category.name'].value_counts().head(10)
    print(f"\n📂 Top 10 类别:")
    for category, count in category_counts.items():
        print(f"   • {category}: {count} 条记录")
    
    # 分析ID模式
    print(f"\n🔤 ID模式分析:")
    id_prefixes = engine.df['id'].str.extract(r'^([a-zA-Z]+)_').dropna()[0].value_counts().head(10)
    for prefix, count in id_prefixes.items():
        print(f"   • {prefix}_*: {count} 条记录")

def demo_search_optimization(engine):
    """演示搜索优化技巧"""
    print_separator("搜索优化技巧")
    
    print("💡 搜索技巧演示:")
    
    # 技巧1: 使用关键词
    print(f"\n1️⃣ 关键词搜索:")
    query = "earnings"
    results = engine.hybrid_search(query, top_k=3)
    print(f"   查询: '{query}' -> {len(results)} 个结果")
    
    # 技巧2: 使用完整短语
    print(f"\n2️⃣ 完整短语搜索:")
    query = "earnings per share"
    results = engine.hybrid_search(query, top_k=3)
    print(f"   查询: '{query}' -> {len(results)} 个结果")
    
    # 技巧3: 使用ID模式
    print(f"\n3️⃣ ID模式搜索:")
    query = "act_12m"
    results = engine.hybrid_search(query, top_k=3)
    print(f"   查询: '{query}' -> {len(results)} 个结果")
    
    # 技巧4: 使用数据集名称
    print(f"\n4️⃣ 数据集搜索:")
    query = "Broker Estimates"
    results = engine.hybrid_search(query, top_k=3)
    print(f"   查询: '{query}' -> {len(results)} 个结果")

def demo_performance_comparison(engine):
    """演示性能对比"""
    print_separator("性能对比")
    
    test_queries = ["earnings", "EBITDA", "analyst", "revenue", "profit"]
    
    print("⚡ 不同搜索方法的性能对比:")
    print(f"{'方法':<15} {'平均耗时(秒)':<15} {'结果数量':<10}")
    print("-" * 40)
    
    # 测试精确搜索
    total_time = 0
    total_results = 0
    for query in test_queries:
        start_time = time.time()
        results = engine.exact_search(query, top_k=5)
        total_time += time.time() - start_time
        total_results += len(results)
    
    avg_time = total_time / len(test_queries)
    avg_results = total_results / len(test_queries)
    print(f"{'精确搜索':<15} {avg_time:<15.2f} {avg_results:<10.1f}")
    
    # 测试模糊搜索
    total_time = 0
    total_results = 0
    for query in test_queries:
        start_time = time.time()
        results = engine.fuzzy_search(query, top_k=5)
        total_time += time.time() - start_time
        total_results += len(results)
    
    avg_time = total_time / len(test_queries)
    avg_results = total_results / len(test_queries)
    print(f"{'模糊搜索':<15} {avg_time:<15.2f} {avg_results:<10.1f}")
    
    # 测试TF-IDF搜索
    total_time = 0
    total_results = 0
    for query in test_queries:
        start_time = time.time()
        results = engine.tfidf_search(query, top_k=5)
        total_time += time.time() - start_time
        total_results += len(results)
    
    avg_time = total_time / len(test_queries)
    avg_results = total_results / len(test_queries)
    print(f"{'TF-IDF搜索':<15} {avg_time:<15.2f} {avg_results:<10.1f}")

def main():
    """主演示函数"""
    print("🔍 中文精准模糊搜索系统 - 功能演示")
    print("="*60)
    
    print("📊 正在加载数据和初始化搜索引擎...")
    start_time = time.time()
    
    # 初始化搜索引擎
    engine = SimpleSearchEngine()
    engine.load_data('data.csv')
    
    init_time = time.time() - start_time
    print(f"✅ 初始化完成! 耗时: {init_time:.2f}秒")
    print(f"📈 数据统计: {len(engine.df)} 条记录")
    
    # 运行各种演示
    try:
        demo_search_types(engine)
        demo_multilingual_search(engine)
        demo_field_analysis(engine)
        demo_search_optimization(engine)
        demo_performance_comparison(engine)
        
        print_separator("演示完成")
        print("🎉 所有演示已完成!")
        print("💡 提示: 可以使用 python search_api.py 启动Web界面")
        print("🌐 Web地址: http://localhost:5000")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
