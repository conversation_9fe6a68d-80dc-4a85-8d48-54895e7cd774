<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整BERT模式 - 企业级因子搜索系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .search-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .full-bert-badge {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .compact-item {
            padding: 0.4rem 0.75rem;
            min-height: auto;
        }
        
        .compact-item .fw-bold {
            font-size: 0.85rem;
            margin-bottom: 0.1rem;
        }
        
        .compact-item .text-muted {
            font-size: 0.75rem;
            line-height: 1.2;
        }
        
        .result-overview-item.active {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            max-width: 400px;
        }
        
        .bert-power-indicator {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border: none;
            color: #333;
            font-weight: bold;
        }
        
        .performance-stats {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- 加载覆盖层 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5 id="loading-title">初始化完整BERT模式</h5>
            <p id="loading-message">正在预计算所有向量...</p>
            <div class="progress mb-2">
                <div id="loading-progress" class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%"></div>
            </div>
            <small id="loading-details" class="text-muted">第一次运行需要更多时间，之后会非常快</small>
        </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="text-center mb-4">
                        <h1 class="display-5 fw-bold">
                            <i class="fas fa-brain me-3"></i>
                            企业级因子搜索系统
                            <span class="full-bert-badge">
                                <i class="fas fa-rocket me-1"></i>完整BERT模式
                            </span>
                        </h1>
                        <p class="lead">发挥BERT完整能力 • 预计算向量 • 极速响应</p>
                    </div>
                    
                    <!-- 数据集选择 -->
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <select class="form-select" id="dataset-select">
                                <option value="">选择数据集...</option>
                                {% for file in data_files %}
                                <option value="{{ file.filename }}">{{ file.display_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-light w-100" id="load-dataset-btn">
                                <i class="fas fa-database me-2"></i>加载数据集
                            </button>
                        </div>
                    </div>
                    
                    <!-- 搜索框 -->
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" id="search-input" 
                               placeholder="输入搜索关键词，如：earnings per share, EBITDA, revenue growth...">
                        <button class="btn btn-light" type="button" id="search-btn">
                            <i class="fas fa-search me-2"></i>搜索
                        </button>
                    </div>
                    
                    <!-- 性能统计 -->
                    <div id="performance-stats" class="performance-stats mt-3" style="display: none;">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="fw-bold" id="search-time">0.000s</div>
                                <small>搜索时间</small>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold" id="total-results">0</div>
                                <small>结果数量</small>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold" id="vector-count">0</div>
                                <small>预计算向量</small>
                            </div>
                            <div class="col-md-3">
                                <div class="fw-bold">完整BERT</div>
                                <small>搜索模式</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 左侧：搜索结果列表 -->
            <div class="col-md-5">
                <div id="results-container" style="display: none;">
                    <!-- 结果将在这里动态加载 -->
                </div>
                
                <!-- 初始状态 -->
                <div id="initial-state" class="text-center py-5">
                    <i class="fas fa-brain fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">完整BERT模式已就绪</h4>
                    <p class="text-muted">选择数据集并输入搜索关键词开始使用</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>完整BERT模式特色：</strong><br>
                        • 预计算所有文本向量，无性能牺牲<br>
                        • 发挥BERT完整语义理解能力<br>
                        • 第一次运行后极速响应
                    </div>
                </div>
            </div>
            
            <!-- 右侧：详细信息 -->
            <div class="col-md-7">
                <div id="details-container" style="display: none;">
                    <!-- 详细信息将在这里显示 -->
                </div>
                
                <!-- 初始状态 -->
                <div id="details-initial-state" class="text-center py-5">
                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">选择因子查看详情</h4>
                    <p class="text-muted">点击左侧搜索结果中的任意因子查看详细信息</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 阈值调整按钮 -->
    <div class="position-fixed bottom-0 end-0 p-3">
        <div class="btn-group-vertical" role="group">
            <button type="button" class="btn btn-outline-primary btn-sm" id="more-results-btn">
                <i class="fas fa-plus me-1"></i>更多结果
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" id="more-precise-btn">
                <i class="fas fa-crosshairs me-1"></i>更精确
            </button>
            <button type="button" class="btn btn-outline-info btn-sm" id="reset-btn">
                <i class="fas fa-undo me-1"></i>重置
            </button>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 应用脚本 -->
    <script src="{{ url_for('static', filename='js/full_bert_app.js') }}"></script>
</body>
</html>
