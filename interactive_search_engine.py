#!/usr/bin/env python3
"""
交互式搜索引擎
支持用户通过简单指令动态调整搜索阈值
- 用户可以输入"更多结果"来降低阈值
- 用户可以输入"更精准"来提高阈值
- 系统会记住用户的阈值偏好并自动应用
"""

import time
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import json
from dataclasses import dataclass, asdict
from collections import defaultdict

# 导入我们的搜索引擎和阈值管理器
from single_bert_fusion_engine import SingleBERTFusionEngine, SearchResult
from dynamic_threshold_manager import (
    DynamicThresholdManager, ThresholdConfig, SearchContext, QueryType
)

@dataclass
class UserSession:
    """用户会话"""
    session_id: str
    user_thresholds: Dict[str, float]  # 用户个性化阈值
    search_history: List[Dict[str, Any]]  # 搜索历史
    preference_score: float = 0.5  # 用户偏好分数 (0=更多结果, 1=更精准)
    last_query: str = ""
    last_results_count: int = 0
    last_avg_score: float = 0.0

class InteractiveSearchEngine:
    """交互式搜索引擎"""
    
    def __init__(self, model_name: str = "paraphrase-multilingual-MiniLM-L12-v2"):
        """初始化交互式搜索引擎"""
        print("🤖 Initializing Interactive Search Engine...")
        
        # 核心搜索引擎
        self.engine = SingleBERTFusionEngine(model_name)
        
        # 用户会话管理
        self.user_sessions = {}
        self.current_session = None
        
        # 阈值调整参数
        self.threshold_adjustment_step = 0.05  # 每次调整的步长
        self.min_threshold_limit = 0.001      # 最低阈值限制
        self.max_threshold_limit = 0.9        # 最高阈值限制
        
        # 交互指令映射
        self.interaction_commands = {
            # 降低阈值的指令
            "更多结果": "more_results",
            "更多": "more_results", 
            "多一些": "more_results",
            "召回率": "more_results",
            "more": "more_results",
            "more results": "more_results",
            "recall": "more_results",
            "broader": "more_results",
            
            # 提高阈值的指令
            "更精准": "more_precise",
            "精准": "more_precise",
            "准确": "more_precise", 
            "精确": "more_precise",
            "质量": "more_precise",
            "precise": "more_precise",
            "accurate": "more_precise",
            "precision": "more_precise",
            "quality": "more_precise",
            
            # 重置阈值的指令
            "重置": "reset",
            "默认": "reset",
            "reset": "reset",
            "default": "reset"
        }
        
        print("✅ Interactive Search Engine initialized!")
    
    def create_user_session(self, session_id: str = "default") -> UserSession:
        """创建用户会话"""
        session = UserSession(
            session_id=session_id,
            user_thresholds={
                'min_score': 0.01,
                'semantic': 0.3,
                'keyword': 0.2
            },
            search_history=[]
        )
        
        self.user_sessions[session_id] = session
        self.current_session = session
        
        print(f"👤 Created user session: {session_id}")
        return session
    
    def load_data(self, file_path: str):
        """加载数据"""
        print(f"📊 Loading data from {file_path}...")
        df = pd.read_csv(file_path)
        self.engine.build_fusion_index(df)
        print(f"✅ Data loaded and indexed!")
    
    def parse_user_input(self, user_input: str) -> Tuple[str, Optional[str]]:
        """解析用户输入"""
        user_input_lower = user_input.lower().strip()
        
        # 检查是否是交互指令
        for command, action in self.interaction_commands.items():
            if command.lower() in user_input_lower:
                return action, user_input_lower.replace(command.lower(), "").strip()
        
        # 如果不是交互指令，则是搜索查询
        return "search", user_input
    
    def adjust_thresholds(self, action: str, session: UserSession) -> Dict[str, float]:
        """调整阈值"""
        current_thresholds = session.user_thresholds.copy()
        
        if action == "more_results":
            # 降低阈值以获得更多结果
            current_thresholds['min_score'] = max(
                self.min_threshold_limit,
                current_thresholds['min_score'] - self.threshold_adjustment_step
            )
            current_thresholds['semantic'] = max(
                0.1,
                current_thresholds['semantic'] - self.threshold_adjustment_step
            )
            current_thresholds['keyword'] = max(
                0.1,
                current_thresholds['keyword'] - self.threshold_adjustment_step
            )
            
            # 更新用户偏好
            session.preference_score = max(0.0, session.preference_score - 0.1)
            
            print(f"📉 降低阈值以获得更多结果")
            
        elif action == "more_precise":
            # 提高阈值以获得更精准结果
            current_thresholds['min_score'] = min(
                self.max_threshold_limit,
                current_thresholds['min_score'] + self.threshold_adjustment_step
            )
            current_thresholds['semantic'] = min(
                0.9,
                current_thresholds['semantic'] + self.threshold_adjustment_step
            )
            current_thresholds['keyword'] = min(
                0.9,
                current_thresholds['keyword'] + self.threshold_adjustment_step
            )
            
            # 更新用户偏好
            session.preference_score = min(1.0, session.preference_score + 0.1)
            
            print(f"📈 提高阈值以获得更精准结果")
            
        elif action == "reset":
            # 重置到默认阈值
            current_thresholds = {
                'min_score': 0.01,
                'semantic': 0.3,
                'keyword': 0.2
            }
            session.preference_score = 0.5
            
            print(f"🔄 重置阈值到默认值")
        
        # 更新会话阈值
        session.user_thresholds = current_thresholds
        
        print(f"🎛️ 当前阈值 - 最小分数: {current_thresholds['min_score']:.3f}, "
              f"语义: {current_thresholds['semantic']:.3f}, "
              f"关键词: {current_thresholds['keyword']:.3f}")
        
        return current_thresholds
    
    def create_custom_threshold_config(self, session: UserSession, 
                                     base_config: ThresholdConfig) -> ThresholdConfig:
        """创建自定义阈值配置"""
        custom_config = ThresholdConfig(**asdict(base_config))
        
        # 应用用户个性化阈值
        user_thresholds = session.user_thresholds
        custom_config.min_score_threshold = user_thresholds['min_score']
        custom_config.semantic_threshold = user_thresholds['semantic']
        custom_config.keyword_threshold = user_thresholds['keyword']
        
        # 根据用户偏好调整目标结果数
        if session.preference_score < 0.3:  # 偏好更多结果
            custom_config.target_results = int(custom_config.target_results * 1.5)
        elif session.preference_score > 0.7:  # 偏好更精准
            custom_config.target_results = max(3, int(custom_config.target_results * 0.7))
        
        return custom_config
    
    def search(self, query: str, session_id: str = "default") -> List[SearchResult]:
        """执行搜索"""
        # 获取或创建用户会话
        if session_id not in self.user_sessions:
            self.create_user_session(session_id)
        
        session = self.user_sessions[session_id]
        self.current_session = session
        
        # 获取基础阈值配置
        base_thresholds = self.engine.threshold_manager.get_adaptive_thresholds(
            query, SearchContext.INTERACTIVE
        )
        
        # 创建自定义阈值配置
        custom_thresholds = self.create_custom_threshold_config(session, base_thresholds)
        
        # 临时替换引擎的阈值管理器配置
        query_type = self.engine.threshold_manager.classify_query_type(query)
        original_config = self.engine.threshold_manager.query_type_configs[query_type]
        self.engine.threshold_manager.query_type_configs[query_type] = custom_thresholds
        
        try:
            # 执行搜索
            results = self.engine.bert_fusion_search(
                query, 
                top_k=custom_thresholds.target_results,
                search_context=SearchContext.INTERACTIVE
            )
            
            # 记录搜索历史
            search_record = {
                'query': query,
                'timestamp': time.time(),
                'results_count': len(results),
                'avg_score': np.mean([r.overall_score for r in results]) if results else 0.0,
                'thresholds': session.user_thresholds.copy(),
                'preference_score': session.preference_score
            }
            
            session.search_history.append(search_record)
            session.last_query = query
            session.last_results_count = len(results)
            session.last_avg_score = search_record['avg_score']
            
            return results
            
        finally:
            # 恢复原始配置
            self.engine.threshold_manager.query_type_configs[query_type] = original_config
    
    def handle_interaction(self, user_input: str, session_id: str = "default") -> List[SearchResult]:
        """处理用户交互"""
        action, query = self.parse_user_input(user_input)
        
        # 获取或创建用户会话
        if session_id not in self.user_sessions:
            self.create_user_session(session_id)
        
        session = self.user_sessions[session_id]
        
        if action in ["more_results", "more_precise", "reset"]:
            # 阈值调整指令
            self.adjust_thresholds(action, session)
            
            # 如果有上一次查询，重新执行搜索
            if session.last_query:
                print(f"🔄 重新搜索: '{session.last_query}'")
                return self.search(session.last_query, session_id)
            else:
                print("💡 请输入搜索查询")
                return []
        
        elif action == "search":
            # 搜索查询
            return self.search(query, session_id)
        
        else:
            print("❓ 未识别的指令")
            return []
    
    def get_session_stats(self, session_id: str = "default") -> Dict[str, Any]:
        """获取会话统计信息"""
        if session_id not in self.user_sessions:
            return {}
        
        session = self.user_sessions[session_id]
        
        if not session.search_history:
            return {'message': 'No search history'}
        
        # 计算统计信息
        total_searches = len(session.search_history)
        avg_results = np.mean([s['results_count'] for s in session.search_history])
        avg_score = np.mean([s['avg_score'] for s in session.search_history if s['avg_score'] > 0])
        
        # 阈值变化趋势
        threshold_history = [s['thresholds']['min_score'] for s in session.search_history]
        threshold_trend = "稳定"
        if len(threshold_history) > 1:
            if threshold_history[-1] < threshold_history[0]:
                threshold_trend = "降低"
            elif threshold_history[-1] > threshold_history[0]:
                threshold_trend = "提高"
        
        return {
            'session_id': session_id,
            'total_searches': total_searches,
            'avg_results_per_search': avg_results,
            'avg_score': avg_score,
            'current_thresholds': session.user_thresholds,
            'preference_score': session.preference_score,
            'threshold_trend': threshold_trend,
            'last_query': session.last_query,
            'last_results_count': session.last_results_count
        }
    
    def print_help(self):
        """打印帮助信息"""
        print("\n📖 Interactive Search Engine Help")
        print("=" * 50)
        print("🔍 搜索指令:")
        print("   直接输入查询内容进行搜索")
        print("   例如: '每股收益', 'earnings per share', '分析师预测'")
        
        print("\n🎛️ 阈值调整指令:")
        print("   更多结果/更多/more - 降低阈值，获得更多结果")
        print("   更精准/精准/precise - 提高阈值，获得更精准结果") 
        print("   重置/reset - 重置阈值到默认值")
        
        print("\n💡 使用技巧:")
        print("   1. 如果搜索结果太少，输入'更多结果'")
        print("   2. 如果搜索结果质量不高，输入'更精准'")
        print("   3. 系统会记住你的阈值偏好")
        print("   4. 输入'stats'查看搜索统计")
        print("   5. 输入'help'查看此帮助")
        print("   6. 输入'quit'退出")

def interactive_demo():
    """交互式演示"""
    print("🚀 Interactive Search Engine Demo")
    print("=" * 60)
    
    # 初始化搜索引擎
    search_engine = InteractiveSearchEngine()
    
    # 加载测试数据
    search_engine.load_data("split_files/USA_1_TOP3000.csv")
    
    # 创建默认会话
    session = search_engine.create_user_session("demo_user")
    
    print("\n🎉 Interactive Search Engine Ready!")
    search_engine.print_help()
    
    print("\n" + "="*60)
    print("开始交互式搜索 (输入 'help' 查看帮助, 'quit' 退出)")
    print("="*60)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n🔍 请输入搜索查询或指令: ").strip()
            
            if not user_input:
                continue
            
            # 处理特殊指令
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见!")
                break
            elif user_input.lower() in ['help', '帮助']:
                search_engine.print_help()
                continue
            elif user_input.lower() in ['stats', '统计']:
                stats = search_engine.get_session_stats("demo_user")
                print(f"\n📊 搜索统计:")
                for key, value in stats.items():
                    if isinstance(value, float):
                        print(f"   {key}: {value:.3f}")
                    else:
                        print(f"   {key}: {value}")
                continue
            
            # 处理搜索和阈值调整
            start_time = time.time()
            results = search_engine.handle_interaction(user_input, "demo_user")
            search_time = time.time() - start_time
            
            # 显示结果
            if results:
                print(f"\n✅ 找到 {len(results)} 个结果 (用时 {search_time:.3f}s):")
                print("-" * 50)
                
                for i, result in enumerate(results[:5], 1):  # 显示前5个结果
                    print(f"{i}. {result.id} (分数: {result.overall_score:.3f})")
                    print(f"   描述: {result.description[:80]}...")
                    print(f"   数据集: {result.dataset_name}")
                    print(f"   类别: {result.category_name}")
                    print()
                
                if len(results) > 5:
                    print(f"... 还有 {len(results) - 5} 个结果")
                
                # 提示用户可以调整阈值
                print("💡 提示: 输入'更多结果'获得更多结果，输入'更精准'获得更精准结果")
                
            else:
                print("❌ 未找到匹配结果")
                print("💡 提示: 尝试输入'更多结果'降低搜索阈值")
        
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            continue

if __name__ == "__main__":
    interactive_demo()
