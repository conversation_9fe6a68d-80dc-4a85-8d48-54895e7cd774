#!/usr/bin/env python3
"""
混合搜索引擎 - 结合向量搜索和传统搜索，实现智能排序和结果融合
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from vector_search_engine import VectorSearchEngine
from traditional_search_engine import TraditionalSearchEngine
import re
from collections import defaultdict

class HybridSearchEngine:
    """混合搜索引擎"""
    
    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        """
        初始化混合搜索引擎
        
        Args:
            model_name: 向量化模型名称
        """
        self.vector_engine = VectorSearchEngine(model_name)
        self.traditional_engine = TraditionalSearchEngine()
        self.df = None
        
    def load_processed_data(self, data_dir: str = "processed_data"):
        """
        加载预处理后的数据
        
        Args:
            data_dir: 数据目录
        """
        print("Loading data for hybrid search engine...")
        self.vector_engine.load_processed_data(data_dir)
        self.traditional_engine.load_processed_data(data_dir)
        self.df = self.vector_engine.df
        print("Hybrid search engine ready!")
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        分析查询特征
        
        Args:
            query: 查询文本
            
        Returns:
            查询分析结果
        """
        analysis = {
            'original_query': query,
            'query_length': len(query),
            'has_chinese': bool(re.search(r'[\u4e00-\u9fff]', query)),
            'has_english': bool(re.search(r'[a-zA-Z]', query)),
            'has_numbers': bool(re.search(r'\d', query)),
            'has_special_chars': bool(re.search(r'[^\w\s\u4e00-\u9fff]', query)),
            'word_count': len(query.split()),
            'is_short_query': len(query.split()) <= 2,
            'is_long_query': len(query.split()) >= 5,
            'contains_id_pattern': bool(re.search(r'[a-zA-Z]+_[a-zA-Z0-9_]+', query)),
            'search_strategy': 'hybrid'
        }
        
        # 根据查询特征决定搜索策略
        if analysis['contains_id_pattern']:
            analysis['search_strategy'] = 'exact_first'
        elif analysis['is_short_query'] and not analysis['has_chinese']:
            analysis['search_strategy'] = 'traditional_first'
        elif analysis['is_long_query'] or analysis['has_chinese']:
            analysis['search_strategy'] = 'vector_first'
        
        return analysis
    
    def normalize_scores(self, results: List[Dict[str, Any]], 
                        search_type: str) -> List[Dict[str, Any]]:
        """
        标准化搜索结果分数
        
        Args:
            results: 搜索结果
            search_type: 搜索类型
            
        Returns:
            标准化后的结果
        """
        if not results:
            return results
        
        # 根据搜索类型进行不同的标准化
        if search_type == 'vector':
            # 向量搜索分数通常在0-1之间
            max_score = max(result['score'] for result in results)
            if max_score > 0:
                for result in results:
                    result['normalized_score'] = result['score'] / max_score
            else:
                for result in results:
                    result['normalized_score'] = 0
                    
        elif search_type in ['exact', 'fuzzy']:
            # 传统搜索分数通常在0-100之间
            for result in results:
                result['normalized_score'] = result['score'] / 100.0
                
        elif search_type == 'tfidf':
            # TF-IDF分数需要特殊处理
            max_score = max(result['score'] for result in results) if results else 1
            for result in results:
                result['normalized_score'] = result['score'] / max_score
        
        else:
            # 默认标准化
            scores = [result['score'] for result in results]
            if scores:
                min_score = min(scores)
                max_score = max(scores)
                score_range = max_score - min_score
                
                if score_range > 0:
                    for result in results:
                        result['normalized_score'] = (result['score'] - min_score) / score_range
                else:
                    for result in results:
                        result['normalized_score'] = 1.0
        
        return results
    
    def merge_results(self, *result_lists: List[List[Dict[str, Any]]], 
                     weights: Optional[List[float]] = None) -> List[Dict[str, Any]]:
        """
        合并多个搜索结果列表
        
        Args:
            result_lists: 多个搜索结果列表
            weights: 各搜索方法的权重
            
        Returns:
            合并后的结果列表
        """
        if not result_lists:
            return []
        
        if weights is None:
            weights = [1.0] * len(result_lists)
        
        # 收集所有结果，按ID分组
        id_to_results = defaultdict(list)
        
        for i, results in enumerate(result_lists):
            weight = weights[i]
            for result in results:
                result_copy = result.copy()
                result_copy['weight'] = weight
                result_copy['weighted_score'] = result_copy.get('normalized_score', 0) * weight
                id_to_results[result['id']].append(result_copy)
        
        # 合并同ID的结果
        merged_results = []
        for item_id, item_results in id_to_results.items():
            # 选择最佳结果作为基础
            best_result = max(item_results, key=lambda x: x['weighted_score'])
            
            # 计算综合分数
            total_weighted_score = sum(r['weighted_score'] for r in item_results)
            total_weight = sum(r['weight'] for r in item_results)
            
            if total_weight > 0:
                final_score = total_weighted_score / total_weight
            else:
                final_score = 0
            
            # 创建最终结果
            merged_result = best_result.copy()
            merged_result['final_score'] = final_score
            merged_result['search_methods'] = [r['search_type'] for r in item_results]
            merged_result['method_count'] = len(item_results)
            
            merged_results.append(merged_result)
        
        # 按最终分数排序
        merged_results.sort(key=lambda x: x['final_score'], reverse=True)
        
        # 重新设置排名
        for i, result in enumerate(merged_results):
            result['rank'] = i + 1
        
        return merged_results
    
    def hybrid_search(self, query: str, top_k: int = 10, 
                     enable_vector: bool = True,
                     enable_exact: bool = True,
                     enable_fuzzy: bool = True,
                     enable_tfidf: bool = True) -> List[Dict[str, Any]]:
        """
        执行混合搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            enable_vector: 是否启用向量搜索
            enable_exact: 是否启用精确搜索
            enable_fuzzy: 是否启用模糊搜索
            enable_tfidf: 是否启用TF-IDF搜索
            
        Returns:
            搜索结果列表
        """
        # 分析查询
        query_analysis = self.analyze_query(query)
        
        # 收集所有搜索结果
        all_results = []
        weights = []
        
        # 根据查询分析调整搜索策略
        if query_analysis['search_strategy'] == 'exact_first':
            # ID模式查询，优先精确搜索
            exact_weight = 0.5
            vector_weight = 0.3
            fuzzy_weight = 0.1
            tfidf_weight = 0.1
        elif query_analysis['search_strategy'] == 'traditional_first':
            # 短查询，优先传统搜索
            exact_weight = 0.3
            vector_weight = 0.2
            fuzzy_weight = 0.3
            tfidf_weight = 0.2
        elif query_analysis['search_strategy'] == 'vector_first':
            # 长查询或中文查询，优先向量搜索
            exact_weight = 0.1
            vector_weight = 0.5
            fuzzy_weight = 0.2
            tfidf_weight = 0.2
        else:
            # 默认权重
            exact_weight = 0.25
            vector_weight = 0.35
            fuzzy_weight = 0.2
            tfidf_weight = 0.2
        
        # 执行向量搜索
        if enable_vector:
            try:
                vector_results = self.vector_engine.vector_search(query, top_k * 2)
                vector_results = self.normalize_scores(vector_results, 'vector')
                all_results.append(vector_results)
                weights.append(vector_weight)
            except Exception as e:
                print(f"Vector search failed: {e}")
        
        # 执行精确搜索
        if enable_exact:
            try:
                exact_results = self.traditional_engine.exact_match_search(query, top_k * 2)
                exact_results = self.normalize_scores(exact_results, 'exact')
                all_results.append(exact_results)
                weights.append(exact_weight)
            except Exception as e:
                print(f"Exact search failed: {e}")
        
        # 执行模糊搜索
        if enable_fuzzy:
            try:
                fuzzy_results = self.traditional_engine.fuzzy_search(query, top_k * 2)
                fuzzy_results = self.normalize_scores(fuzzy_results, 'fuzzy')
                all_results.append(fuzzy_results)
                weights.append(fuzzy_weight)
            except Exception as e:
                print(f"Fuzzy search failed: {e}")
        
        # 执行TF-IDF搜索
        if enable_tfidf:
            try:
                tfidf_results = self.traditional_engine.tfidf_search(query, top_k * 2)
                tfidf_results = self.normalize_scores(tfidf_results, 'tfidf')
                all_results.append(tfidf_results)
                weights.append(tfidf_weight)
            except Exception as e:
                print(f"TF-IDF search failed: {e}")
        
        # 合并结果
        if all_results:
            merged_results = self.merge_results(*all_results, weights=weights)
            return merged_results[:top_k]
        else:
            return []
    
    def smart_search(self, query: str, top_k: int = 10) -> Dict[str, Any]:
        """
        智能搜索 - 自动选择最佳搜索策略
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            包含搜索结果和元信息的字典
        """
        # 分析查询
        query_analysis = self.analyze_query(query)
        
        # 执行混合搜索
        results = self.hybrid_search(query, top_k)
        
        # 返回完整信息
        return {
            'query': query,
            'query_analysis': query_analysis,
            'results': results,
            'total_results': len(results),
            'search_time': None,  # 可以添加计时功能
            'search_strategy': query_analysis['search_strategy']
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索引擎统计信息"""
        vector_stats = self.vector_engine.get_statistics()
        
        return {
            'engine_type': 'hybrid',
            'vector_engine_stats': vector_stats,
            'traditional_engine_available': self.traditional_engine.df is not None,
            'total_records': len(self.df) if self.df is not None else 0
        }

if __name__ == "__main__":
    # 示例用法
    engine = HybridSearchEngine()
    
    # 加载数据
    engine.load_processed_data()
    
    # 测试智能搜索
    test_queries = [
        "earnings per share",
        "每股收益",
        "act_12m_eps_value",
        "分析师估算数据"
    ]
    
    for query in test_queries:
        print(f"\n搜索查询: '{query}'")
        result = engine.smart_search(query, top_k=3)
        
        print(f"查询分析: {result['query_analysis']['search_strategy']}")
        print("搜索结果:")
        
        for res in result['results']:
            print(f"  Rank {res['rank']}: {res['id']}")
            print(f"    Score: {res['final_score']:.4f}")
            print(f"    Methods: {res['search_methods']}")
            print(f"    Description: {res['description'][:100]}...")
            print("---")
