#!/usr/bin/env python3
"""
简化版多模型融合中文搜索系统测试
专注于验证特征融合和中文支持能力，不依赖复杂的深度学习库
"""

import time
import pandas as pd
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple
import jieba
import jieba.posseg as pseg
import jieba.analyse
import re
from collections import defaultdict, Counter
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

class ChineseNLPEnhancer:
    """中文NLP增强器"""
    
    def __init__(self):
        self.financial_terms = self._load_financial_terms()
        self.stop_words = self._load_stop_words()
        self.synonym_dict = self._load_synonyms()
        self.entity_patterns = self._build_entity_patterns()
        
        # 初始化jieba
        self._init_jieba()
    
    def _load_financial_terms(self) -> set:
        """加载金融术语"""
        return {
            # 中文金融术语
            '每股收益', '净资产收益率', '总资产收益率', '毛利率', '净利率',
            '营业利润率', '资产负债率', '流动比率', '速动比率', '市盈率',
            '市净率', '市销率', '营业收入', '净利润', '毛利润', '现金流',
            '自由现金流', '经营现金流', '分析师', '预测', '评级', '目标价',
            '买入', '卖出', '持有', '增持', '减持', '中性', '推荐',
            
            # 英文金融术语
            'EPS', 'ROE', 'ROA', 'PE', 'PB', 'PS', 'EBITDA', 'EBIT',
            'earnings', 'revenue', 'profit', 'cash flow', 'analyst',
            'estimate', 'forecast', 'rating', 'target price'
        }
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        return {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都',
            '一', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着',
            '没有', '看', '好', '自己', '这', '那', '里', '就是', '还是',
            '为了', '还有', '可以', '这个', '那个', '什么', '怎么', '为什么'
        }
    
    def _load_synonyms(self) -> Dict[str, List[str]]:
        """加载同义词"""
        return {
            '每股收益': ['EPS', '每股盈利', '每股净收益'],
            '净资产收益率': ['ROE', '股东权益回报率'],
            '市盈率': ['PE', 'P/E', '价格收益比'],
            '分析师': ['研究员', '分析员'],
            '预测': ['预期', '预估', '估算'],
            '增长': ['增长率', '增速', '涨幅'],
            '营业收入': ['营收', '销售收入', '收入'],
            '净利润': ['净收益', '税后利润']
        }
    
    def _build_entity_patterns(self) -> Dict[str, re.Pattern]:
        """构建实体识别模式"""
        return {
            'percentage': re.compile(r'\d+\.?\d*%'),
            'currency': re.compile(r'[¥$€£]\d+\.?\d*[万亿千百十]?'),
            'time_period': re.compile(r'\d+[年月日季度]|[上下]半年|年初|年末'),
            'financial_ratio': re.compile(r'(每股|净资产|总资产|营业|利润|收入).{0,10}(率|比|额|值)')
        }
    
    def _init_jieba(self):
        """初始化jieba"""
        for term in self.financial_terms:
            jieba.add_word(term, freq=2000, tag='financial')
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取实体"""
        entities = defaultdict(list)
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = pattern.findall(text)
            if matches:
                entities[entity_type].extend(matches)
        
        return dict(entities)
    
    def analyze_text(self, text: str) -> Dict[str, Any]:
        """分析文本"""
        if not text or pd.isna(text):
            return {
                'keywords': [],
                'entities': {},
                'sentiment': 'neutral',
                'topics': [],
                'pos_tags': []
            }
        
        text = str(text)
        
        # 关键词提取
        keywords = jieba.analyse.extract_tags(text, topK=10, withWeight=True)
        
        # 实体提取
        entities = self.extract_entities(text)
        
        # 词性标注
        pos_tags = [(word, pos) for word, pos in pseg.lcut(text)]
        
        # 简单情感分析
        sentiment = self._analyze_sentiment(text)
        
        # 主题识别
        topics = self._identify_topics(text)
        
        return {
            'keywords': keywords,
            'entities': entities,
            'sentiment': sentiment,
            'topics': topics,
            'pos_tags': pos_tags
        }
    
    def _analyze_sentiment(self, text: str) -> str:
        """简单情感分析"""
        positive_words = ['增长', '上涨', '提升', '改善', '优化', '强劲', '良好', '积极']
        negative_words = ['下降', '下跌', '恶化', '减少', '萎缩', '疲软', '困难', '风险']
        
        pos_count = sum(1 for word in positive_words if word in text)
        neg_count = sum(1 for word in negative_words if word in text)
        
        if pos_count > neg_count:
            return 'positive'
        elif neg_count > pos_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _identify_topics(self, text: str) -> List[str]:
        """识别主题"""
        topic_keywords = {
            '盈利能力': ['净利润', '毛利率', '净利率', '每股收益', 'ROE'],
            '偿债能力': ['资产负债率', '流动比率', '债务'],
            '成长能力': ['增长率', '增长', '成长'],
            '估值水平': ['市盈率', '市净率', '估值'],
            '分析师观点': ['分析师', '评级', '目标价', '预测']
        }
        
        identified_topics = []
        for topic, keywords in topic_keywords.items():
            if any(keyword in text for keyword in keywords):
                identified_topics.append(topic)
        
        return identified_topics

class MultiModelFusionSearchEngine:
    """多模型融合搜索引擎"""
    
    def __init__(self):
        self.df = None
        self.nlp_enhancer = ChineseNLPEnhancer()
        self.column_features = {}
        self.tfidf_vectorizers = {}
        self.tfidf_matrices = {}
        
        # 列权重配置
        self.column_weights = {
            'id': 0.15,
            'description': 0.35,
            'dataset.name': 0.20,
            'category.name': 0.15,
            'subcategory.name': 0.15
        }
    
    def load_data(self, file_path: str):
        """加载数据"""
        print(f"📊 Loading data from {file_path}...")
        self.df = pd.read_csv(file_path)
        print(f"✅ Loaded {len(self.df)} records")
        return True
    
    def build_multi_model_index(self):
        """构建多模型融合索引"""
        if self.df is None:
            return False
        
        print("🔨 Building multi-model fusion index...")
        start_time = time.time()
        
        target_columns = list(self.column_weights.keys())
        
        # 为每列构建TF-IDF向量化器
        for col_name in target_columns:
            if col_name in self.df.columns:
                # 收集该列的所有文本
                texts = []
                for idx, row in self.df.iterrows():
                    if col_name in row and pd.notna(row[col_name]):
                        text = str(row[col_name])
                        # 中文分词
                        words = jieba.lcut(text)
                        texts.append(' '.join(words))
                    else:
                        texts.append('')
                
                # 构建TF-IDF向量化器
                if texts:
                    vectorizer = TfidfVectorizer(
                        max_features=5000,
                        ngram_range=(1, 2),
                        min_df=1,
                        max_df=0.9
                    )
                    tfidf_matrix = vectorizer.fit_transform(texts)
                    
                    self.tfidf_vectorizers[col_name] = vectorizer
                    self.tfidf_matrices[col_name] = tfidf_matrix
        
        # 提取每行的特征
        for idx, row in self.df.iterrows():
            if idx % 1000 == 0 and idx > 0:
                print(f"   Processed {idx}/{len(self.df)} records")
            
            column_features = {}
            
            for col_name in target_columns:
                if col_name in row and pd.notna(row[col_name]):
                    text_content = str(row[col_name])
                    
                    # NLP分析
                    nlp_analysis = self.nlp_enhancer.analyze_text(text_content)
                    
                    column_features[col_name] = {
                        'text': text_content,
                        'nlp_analysis': nlp_analysis,
                        'tfidf_index': idx  # TF-IDF矩阵中的索引
                    }
            
            self.column_features[idx] = {
                'columns': column_features,
                'row_data': row.to_dict()
            }
        
        build_time = time.time() - start_time
        print(f"✅ Multi-model index built in {build_time:.2f}s")
        return True
    
    def multi_model_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """多模型融合搜索"""
        if not self.column_features:
            return []
        
        print(f"🔍 Multi-model fusion search for: '{query}'")
        start_time = time.time()
        
        # 分析查询
        query_analysis = self.nlp_enhancer.analyze_text(query)
        query_keywords = [kw for kw, weight in query_analysis['keywords']]
        
        results = []
        
        # 搜索所有文档
        for doc_idx, doc_data in self.column_features.items():
            columns = doc_data['columns']
            row_data = doc_data['row_data']
            
            # 多模型分数计算
            model_scores = {
                'keyword_similarity': 0.0,
                'tfidf_similarity': 0.0,
                'entity_similarity': 0.0,
                'topic_similarity': 0.0,
                'text_matching': 0.0
            }
            
            column_scores = {}
            overall_score = 0.0
            matched_columns = []
            matched_features = []
            
            for col_name, col_data in columns.items():
                col_analysis = col_data['nlp_analysis']
                
                # 1. 关键词相似度
                keyword_sim = self._calculate_keyword_similarity(
                    query_keywords, [kw for kw, w in col_analysis['keywords']]
                )
                
                # 2. TF-IDF相似度
                tfidf_sim = 0.0
                if col_name in self.tfidf_vectorizers:
                    tfidf_sim = self._calculate_tfidf_similarity(
                        query, col_name, col_data['tfidf_index']
                    )
                
                # 3. 实体相似度
                entity_sim = self._calculate_entity_similarity(
                    query_analysis['entities'], col_analysis['entities']
                )
                
                # 4. 主题相似度
                topic_sim = self._calculate_topic_similarity(
                    query_analysis['topics'], col_analysis['topics']
                )
                
                # 5. 文本匹配
                text_sim = self._calculate_text_matching(
                    query.lower(), col_data['text'].lower()
                )
                
                # 综合列分数
                col_score = (
                    keyword_sim * 0.3 +
                    tfidf_sim * 0.25 +
                    entity_sim * 0.2 +
                    topic_sim * 0.15 +
                    text_sim * 0.1
                )
                
                column_scores[col_name] = col_score
                
                # 更新模型分数
                model_scores['keyword_similarity'] += keyword_sim * self.column_weights[col_name]
                model_scores['tfidf_similarity'] += tfidf_sim * self.column_weights[col_name]
                model_scores['entity_similarity'] += entity_sim * self.column_weights[col_name]
                model_scores['topic_similarity'] += topic_sim * self.column_weights[col_name]
                model_scores['text_matching'] += text_sim * self.column_weights[col_name]
                
                # 记录匹配信息
                if col_score > 0.1:
                    matched_columns.append(col_name)
                    matched_features.extend([kw for kw, w in col_analysis['keywords']])
                
                # 加权到总分
                overall_score += col_score * self.column_weights[col_name]
            
            # 创建搜索结果
            if overall_score > 0.01:
                result = {
                    'id': str(row_data.get('id', '')),
                    'description': str(row_data.get('description', '')),
                    'dataset_name': str(row_data.get('dataset.name', '')),
                    'category_name': str(row_data.get('category.name', '')),
                    'subcategory_name': str(row_data.get('subcategory.name', '')),
                    'region': str(row_data.get('region', '')),
                    'universe': str(row_data.get('universe', '')),
                    'delay': str(row_data.get('delay', '')),
                    
                    'overall_score': overall_score,
                    'model_scores': model_scores,
                    'column_scores': column_scores,
                    'matched_columns': matched_columns,
                    'matched_features': list(set(matched_features)),
                    'search_mode': 'multi_model_fusion'
                }
                results.append(result)
        
        # 排序并返回top_k结果
        results.sort(key=lambda x: x['overall_score'], reverse=True)
        
        search_time = time.time() - start_time
        print(f"✅ Search completed in {search_time:.3f}s, found {len(results)} results")
        
        return results[:top_k]
    
    def _calculate_keyword_similarity(self, query_kw: List[str], doc_kw: List[str]) -> float:
        """计算关键词相似度"""
        if not query_kw or not doc_kw:
            return 0.0
        
        query_set = set(kw.lower() for kw in query_kw)
        doc_set = set(kw.lower() for kw in doc_kw)
        
        intersection = query_set & doc_set
        union = query_set | doc_set
        
        jaccard = len(intersection) / len(union) if union else 0.0
        
        # 部分匹配
        partial_score = 0.0
        for q_kw in query_kw:
            for d_kw in doc_kw:
                if q_kw.lower() in d_kw.lower() or d_kw.lower() in q_kw.lower():
                    partial_score += 0.5
        
        partial_score = min(partial_score / len(query_kw), 1.0) if query_kw else 0.0
        
        return jaccard * 0.7 + partial_score * 0.3
    
    def _calculate_tfidf_similarity(self, query: str, col_name: str, doc_idx: int) -> float:
        """计算TF-IDF相似度"""
        if col_name not in self.tfidf_vectorizers:
            return 0.0
        
        try:
            vectorizer = self.tfidf_vectorizers[col_name]
            tfidf_matrix = self.tfidf_matrices[col_name]
            
            # 分词查询
            query_words = jieba.lcut(query)
            query_text = ' '.join(query_words)
            
            # 向量化查询
            query_vector = vectorizer.transform([query_text])
            
            # 计算相似度
            doc_vector = tfidf_matrix[doc_idx:doc_idx+1]
            similarity = cosine_similarity(query_vector, doc_vector)[0][0]
            
            return float(similarity)
            
        except Exception:
            return 0.0
    
    def _calculate_entity_similarity(self, query_entities: Dict, doc_entities: Dict) -> float:
        """计算实体相似度"""
        if not query_entities or not doc_entities:
            return 0.0
        
        total_score = 0.0
        total_types = 0
        
        for entity_type in query_entities:
            if entity_type in doc_entities:
                query_set = set(query_entities[entity_type])
                doc_set = set(doc_entities[entity_type])
                
                intersection = query_set & doc_set
                if query_set:
                    score = len(intersection) / len(query_set)
                    total_score += score
                
                total_types += 1
        
        return total_score / total_types if total_types > 0 else 0.0
    
    def _calculate_topic_similarity(self, query_topics: List[str], doc_topics: List[str]) -> float:
        """计算主题相似度"""
        if not query_topics or not doc_topics:
            return 0.0
        
        query_set = set(query_topics)
        doc_set = set(doc_topics)
        
        intersection = query_set & doc_set
        
        return len(intersection) / len(query_set) if query_set else 0.0
    
    def _calculate_text_matching(self, query_text: str, doc_text: str) -> float:
        """计算文本匹配度"""
        if query_text in doc_text:
            if query_text == doc_text:
                return 1.0
            elif doc_text.startswith(query_text):
                return 0.9
            else:
                return 0.7
        
        # 词汇重叠
        query_words = set(query_text.split())
        doc_words = set(doc_text.split())
        
        if not query_words:
            return 0.0
        
        intersection = query_words & doc_words
        return len(intersection) / len(query_words)

def test_multi_model_chinese():
    """测试多模型中文搜索"""
    print("🤖 多模型融合中文搜索系统测试")
    print("=" * 60)
    
    # 查找测试数据
    split_files_dir = Path("split_files")
    if not split_files_dir.exists():
        print("❌ split_files directory not found")
        return False
    
    csv_files = list(split_files_dir.glob("*.csv"))
    if not csv_files:
        print("❌ No CSV files found")
        return False
    
    # 选择测试文件
    test_file = csv_files[0]
    print(f"📁 Using test file: {test_file.name}")
    
    # 初始化引擎
    engine = MultiModelFusionSearchEngine()
    engine.load_data(str(test_file))
    engine.build_multi_model_index()
    
    # 中文测试查询
    chinese_queries = [
        ("基础中文", "每股收益"),
        ("中文短语", "净资产收益率"),
        ("金融术语", "EBITDA相关指标"),
        ("混合查询", "每股收益 EPS 增长"),
        ("复杂查询", "分析师对公司盈利能力的预测"),
        ("专业术语", "市盈率和市净率估值分析"),
        ("英文查询", "earnings per share"),
        ("英文缩写", "ROE ROA analysis"),
        ("跨语言", "analyst 分析师 rating"),
        ("长句查询", "2023年第三季度营业收入同比增长情况分析")
    ]
    
    print("\n🧪 开始多模型中文搜索测试...")
    
    total_time = 0
    total_results = 0
    successful_queries = 0
    
    for query_type, query in chinese_queries:
        print(f"\n📝 {query_type}: '{query}'")
        
        start_time = time.time()
        results = engine.multi_model_search(query, top_k=5)
        search_time = time.time() - start_time
        
        total_time += search_time
        total_results += len(results)
        
        if results:
            successful_queries += 1
        
        print(f"   ⏱️  搜索时间: {search_time * 1000:.1f}ms")
        print(f"   📊 结果数量: {len(results)}")
        
        # 显示前3个结果
        for i, result in enumerate(results[:3]):
            score = result['overall_score']
            model_scores = result['model_scores']
            matched_cols = result['matched_columns']
            
            print(f"   {i+1}. {result['id']} (总分: {score:.4f})")
            print(f"      描述: {result['description'][:50]}...")
            print(f"      匹配列: {matched_cols}")
            print(f"      模型分数: 关键词={model_scores['keyword_similarity']:.3f}, "
                  f"TF-IDF={model_scores['tfidf_similarity']:.3f}, "
                  f"实体={model_scores['entity_similarity']:.3f}")
    
    # 性能总结
    avg_time = total_time / len(chinese_queries)
    avg_results = total_results / len(chinese_queries)
    success_rate = successful_queries / len(chinese_queries)
    
    print(f"\n📈 多模型中文搜索测试总结:")
    print(f"   平均搜索时间: {avg_time * 1000:.1f}ms")
    print(f"   平均结果数量: {avg_results:.1f}")
    print(f"   成功查询率: {success_rate * 100:.1f}%")
    print(f"   总测试时间: {total_time:.2f}s")
    
    # 性能评估
    if success_rate >= 0.9:
        print("🎉 中文支持能力: 优秀 (成功率 ≥ 90%)")
    elif success_rate >= 0.7:
        print("👍 中文支持能力: 良好 (成功率 ≥ 70%)")
    elif success_rate >= 0.5:
        print("⚠️ 中文支持能力: 一般 (成功率 ≥ 50%)")
    else:
        print("❌ 中文支持能力: 需要改进 (成功率 < 50%)")
    
    print(f"\n🎯 多模型融合优势:")
    print(f"   1. 关键词匹配 - 精确词汇匹配")
    print(f"   2. TF-IDF相似度 - 统计相关性")
    print(f"   3. 实体识别 - 专业术语匹配")
    print(f"   4. 主题分析 - 语义主题理解")
    print(f"   5. 特征融合 - 多列信息综合")
    
    return True

if __name__ == "__main__":
    test_multi_model_chinese()
