#!/usr/bin/env python3
"""
搜索API接口 - 提供易用的搜索接口，支持多种搜索模式和参数
"""

from flask import Flask, request, jsonify
from flask import render_template_string
import time
import traceback
from typing import Dict, Any, List, Optional
from hybrid_search_engine import HybridSearchEngine
from vector_search_engine import VectorSearchEngine
from traditional_search_engine import TraditionalSearchEngine
import os

app = Flask(__name__)

# 全局搜索引擎实例
search_engines = {
    'hybrid': None,
    'vector': None,
    'traditional': None
}

def initialize_engines():
    """初始化搜索引擎"""
    global search_engines
    
    try:
        print("Initializing search engines...")
        
        # 初始化混合搜索引擎
        search_engines['hybrid'] = HybridSearchEngine()
        search_engines['hybrid'].load_processed_data()
        
        # 混合引擎包含了向量和传统引擎
        search_engines['vector'] = search_engines['hybrid'].vector_engine
        search_engines['traditional'] = search_engines['hybrid'].traditional_engine
        
        print("Search engines initialized successfully!")
        return True
        
    except Exception as e:
        print(f"Failed to initialize search engines: {e}")
        traceback.print_exc()
        return False

@app.route('/')
def index():
    """主页 - 搜索界面"""
    html_template = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>中文精准模糊搜索系统</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; margin-bottom: 30px; }
            .search-form { margin-bottom: 30px; }
            .search-input { width: 70%; padding: 12px; font-size: 16px; border: 2px solid #ddd; border-radius: 5px; }
            .search-button { padding: 12px 24px; font-size: 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px; }
            .search-button:hover { background: #0056b3; }
            .options { margin: 20px 0; }
            .option-group { display: inline-block; margin-right: 20px; }
            .results { margin-top: 30px; }
            .result-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: #fafafa; }
            .result-header { font-weight: bold; color: #007bff; margin-bottom: 5px; }
            .result-score { color: #666; font-size: 12px; }
            .result-description { margin: 10px 0; }
            .result-meta { font-size: 12px; color: #888; }
            .loading { text-align: center; color: #666; }
            .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔍 中文精准模糊搜索系统</h1>
            
            <div class="search-form">
                <input type="text" id="searchQuery" class="search-input" placeholder="请输入搜索关键词..." value="">
                <button onclick="performSearch()" class="search-button">搜索</button>
            </div>
            
            <div class="options">
                <div class="option-group">
                    <label>搜索引擎:</label>
                    <select id="searchEngine">
                        <option value="hybrid">混合搜索 (推荐)</option>
                        <option value="vector">向量搜索</option>
                        <option value="traditional">传统搜索</option>
                    </select>
                </div>
                
                <div class="option-group">
                    <label>结果数量:</label>
                    <select id="topK">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
            
            <div id="results" class="results"></div>
        </div>
        
        <script>
            function performSearch() {
                const query = document.getElementById('searchQuery').value.trim();
                const engine = document.getElementById('searchEngine').value;
                const topK = document.getElementById('topK').value;
                const resultsDiv = document.getElementById('results');
                
                if (!query) {
                    alert('请输入搜索关键词');
                    return;
                }
                
                resultsDiv.innerHTML = '<div class="loading">搜索中...</div>';
                
                const url = `/api/search?query=${encodeURIComponent(query)}&engine=${engine}&top_k=${topK}`;
                
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        displayResults(data);
                    })
                    .catch(error => {
                        resultsDiv.innerHTML = `<div class="error">搜索失败: ${error.message}</div>`;
                    });
            }
            
            function displayResults(data) {
                const resultsDiv = document.getElementById('results');
                
                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">${data.error}</div>`;
                    return;
                }
                
                if (!data.results || data.results.length === 0) {
                    resultsDiv.innerHTML = '<div>未找到相关结果</div>';
                    return;
                }
                
                let html = `<h3>搜索结果 (${data.total_results} 条)</h3>`;
                
                data.results.forEach(result => {
                    html += `
                        <div class="result-item">
                            <div class="result-header">
                                ${result.rank}. ${result.id}
                                <span class="result-score">评分: ${result.final_score ? result.final_score.toFixed(4) : result.score.toFixed(4)}</span>
                            </div>
                            <div class="result-description">${result.description}</div>
                            <div class="result-meta">
                                数据集: ${result.dataset_name} | 
                                类别: ${result.category_name} | 
                                子类别: ${result.subcategory_name}
                                ${result.search_methods ? ' | 搜索方法: ' + result.search_methods.join(', ') : ''}
                            </div>
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = html;
            }
            
            // 回车键搜索
            document.getElementById('searchQuery').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        </script>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/api/search', methods=['GET'])
def api_search():
    """搜索API接口"""
    try:
        # 获取参数
        query = request.args.get('query', '').strip()
        engine_type = request.args.get('engine', 'hybrid')
        top_k = int(request.args.get('top_k', 10))
        
        if not query:
            return jsonify({'error': '查询参数不能为空'}), 400
        
        if engine_type not in search_engines:
            return jsonify({'error': f'不支持的搜索引擎类型: {engine_type}'}), 400
        
        engine = search_engines[engine_type]
        if engine is None:
            return jsonify({'error': '搜索引擎未初始化'}), 500
        
        # 记录搜索开始时间
        start_time = time.time()
        
        # 执行搜索
        if engine_type == 'hybrid':
            search_result = engine.smart_search(query, top_k)
            results = search_result['results']
            query_analysis = search_result.get('query_analysis', {})
        elif engine_type == 'vector':
            results = engine.vector_search(query, top_k)
            query_analysis = {}
        elif engine_type == 'traditional':
            # 对于传统搜索，使用TF-IDF作为默认方法
            results = engine.tfidf_search(query, top_k)
            query_analysis = {}
        
        # 计算搜索时间
        search_time = time.time() - start_time
        
        # 构建响应
        response = {
            'query': query,
            'engine': engine_type,
            'results': results,
            'total_results': len(results),
            'search_time': round(search_time, 4),
            'query_analysis': query_analysis
        }
        
        return jsonify(response)
        
    except Exception as e:
        error_msg = f'搜索过程中发生错误: {str(e)}'
        print(error_msg)
        traceback.print_exc()
        return jsonify({'error': error_msg}), 500

@app.route('/api/similar/<item_id>', methods=['GET'])
def api_find_similar(item_id):
    """查找相似项目API"""
    try:
        top_k = int(request.args.get('top_k', 10))
        
        engine = search_engines['vector']
        if engine is None:
            return jsonify({'error': '向量搜索引擎未初始化'}), 500
        
        start_time = time.time()
        results = engine.find_similar_items(item_id, top_k)
        search_time = time.time() - start_time
        
        response = {
            'item_id': item_id,
            'results': results,
            'total_results': len(results),
            'search_time': round(search_time, 4)
        }
        
        return jsonify(response)
        
    except Exception as e:
        error_msg = f'查找相似项目时发生错误: {str(e)}'
        print(error_msg)
        return jsonify({'error': error_msg}), 500

@app.route('/api/stats', methods=['GET'])
def api_stats():
    """获取搜索引擎统计信息"""
    try:
        stats = {}
        
        for engine_name, engine in search_engines.items():
            if engine is not None:
                if hasattr(engine, 'get_statistics'):
                    stats[engine_name] = engine.get_statistics()
                else:
                    stats[engine_name] = {'status': 'available'}
            else:
                stats[engine_name] = {'status': 'not_initialized'}
        
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    status = {
        'status': 'healthy',
        'engines': {}
    }
    
    for engine_name, engine in search_engines.items():
        status['engines'][engine_name] = 'ready' if engine is not None else 'not_ready'
    
    return jsonify(status)

if __name__ == '__main__':
    print("Starting Chinese Fuzzy Search API...")
    
    # 检查是否存在预处理数据
    if not os.path.exists('processed_data'):
        print("Error: Processed data not found!")
        print("Please run data preprocessing first:")
        print("python data_preprocessor.py")
        exit(1)
    
    # 初始化搜索引擎
    if not initialize_engines():
        print("Failed to initialize search engines. Exiting...")
        exit(1)
    
    print("API server starting on http://localhost:5000")
    print("Visit http://localhost:5000 for the web interface")
    print("API endpoints:")
    print("  GET /api/search?query=<query>&engine=<engine>&top_k=<k>")
    print("  GET /api/similar/<item_id>?top_k=<k>")
    print("  GET /api/stats")
    print("  GET /health")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
