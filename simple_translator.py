#!/usr/bin/env python3
"""
简化的翻译系统
使用本地字典进行快速翻译，避免API调用问题
"""

import re
from typing import Dict, Optional

class SimpleTranslator:
    """简化的翻译器"""
    
    def __init__(self):
        self.cache = {}
        
        # 数据集翻译字典
        self.dataset_dict = {
            'Broker Estimates': '券商预测',
            'Fundamental Analysis': '基本面分析',
            'Technical Analysis': '技术分析',
            'Market Data': '市场数据',
            'Financial Statements': '财务报表',
            'Economic Indicators': '经济指标',
            'Company Information': '公司信息',
            'Industry Analysis': '行业分析',
            'Risk Metrics': '风险指标',
            'Valuation Metrics': '估值指标',
            'Growth Metrics': '成长指标',
            'Profitability Metrics': '盈利能力指标',
            'Liquidity Metrics': '流动性指标',
            'Efficiency Metrics': '效率指标',
            'Leverage Metrics': '杠杆指标',
            'Quality Metrics': '质量指标',
            'Momentum Metrics': '动量指标',
            'Sentiment Metrics': '情绪指标'
        }
        
        # 类别翻译字典
        self.category_dict = {
            'Analyst': '分析师',
            'Fundamental': '基本面',
            'Technical': '技术面',
            'Market': '市场',
            'Financial': '财务',
            'Economic': '经济',
            'Company': '公司',
            'Industry': '行业',
            'Risk': '风险',
            'Valuation': '估值',
            'Growth': '成长',
            'Profitability': '盈利能力',
            'Liquidity': '流动性',
            'Efficiency': '效率',
            'Leverage': '杠杆',
            'Quality': '质量',
            'Momentum': '动量',
            'Sentiment': '情绪',
            'ESG': '环境社会治理'
        }
        
        # 子类别翻译字典
        self.subcategory_dict = {
            'Analyst Estimates': '分析师预测',
            'Analyst Recommendations': '分析师建议',
            'Earnings Estimates': '盈利预测',
            'Revenue Estimates': '营收预测',
            'Price Targets': '目标价',
            'Rating Changes': '评级变化',
            'Consensus Estimates': '一致预期',
            'Estimate Revisions': '预测修正',
            'Surprise History': '超预期历史',
            'Forward Looking': '前瞻性',
            'Historical Data': '历史数据',
            'Current Metrics': '当前指标',
            'Trailing Metrics': '滚动指标',
            'Forward Metrics': '前瞻指标',
            'Quarterly Data': '季度数据',
            'Annual Data': '年度数据',
            'Monthly Data': '月度数据',
            'Daily Data': '日度数据',
            'Real Time': '实时数据',
            'End of Day': '收盘数据'
        }
        
        # 描述关键词翻译
        self.description_keywords = {
            # 时间相关
            'actual': '实际',
            'estimate': '预测',
            'forecast': '预测',
            'consensus': '一致',
            'median': '中位数',
            'mean': '平均',
            'high': '最高',
            'low': '最低',
            'current': '当前',
            'previous': '前期',
            'next': '下期',
            'trailing': '滚动',
            'forward': '前瞻',
            'growth': '增长',
            'change': '变化',
            'ratio': '比率',
            'margin': '利润率',
            'return': '回报',
            'yield': '收益率',
            'value': '价值',
            'price': '价格',
            'earnings': '盈利',
            'revenue': '营收',
            'sales': '销售',
            'profit': '利润',
            'income': '收入',
            'cash': '现金',
            'debt': '债务',
            'equity': '权益',
            'assets': '资产',
            'liabilities': '负债',
            'shares': '股份',
            'dividend': '股息',
            'per share': '每股',
            'quarterly': '季度',
            'annual': '年度',
            'monthly': '月度',
            'daily': '日度',
            'year': '年',
            'quarter': '季度',
            'month': '月',
            'week': '周',
            'day': '天'
        }
        
        # 地区翻译
        self.region_dict = {
            'USA': '美国',
            'China': '中国',
            'Japan': '日本',
            'Europe': '欧洲',
            'UK': '英国',
            'Germany': '德国',
            'France': '法国',
            'Canada': '加拿大',
            'Australia': '澳大利亚',
            'South Korea': '韩国',
            'India': '印度',
            'Brazil': '巴西',
            'Russia': '俄罗斯',
            'Global': '全球',
            'Developed Markets': '发达市场',
            'Emerging Markets': '新兴市场',
            'Asia Pacific': '亚太地区',
            'North America': '北美',
            'Latin America': '拉丁美洲',
            'Middle East': '中东',
            'Africa': '非洲'
        }
        
        # 延迟类型翻译
        self.delay_dict = {
            'Real Time': '实时',
            'End of Day': '收盘后',
            '15 min': '15分钟延迟',
            '20 min': '20分钟延迟',
            'Daily': '日更新',
            'Weekly': '周更新',
            'Monthly': '月更新',
            'Quarterly': '季度更新',
            'Annual': '年度更新',
            'Intraday': '盘中',
            'After Hours': '盘后',
            'Pre Market': '盘前'
        }
    
    def translate_to_chinese(self, text: str, field_type: str = 'description') -> str:
        """翻译英文到中文"""
        if not text or not isinstance(text, str):
            return text
        
        # 检查缓存
        cache_key = f"{field_type}:{text}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        # 如果是纯数字或很短，不翻译
        if text.isdigit() or len(text) <= 2:
            return text
        
        # 如果已经包含中文，不翻译
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            return text
        
        # 根据字段类型选择翻译字典
        if field_type == 'dataset':
            translated = self._translate_with_dict(text, self.dataset_dict)
        elif field_type == 'category':
            translated = self._translate_with_dict(text, self.category_dict)
        elif field_type == 'subcategory':
            translated = self._translate_with_dict(text, self.subcategory_dict)
        elif field_type == 'region':
            translated = self._translate_with_dict(text, self.region_dict)
        elif field_type == 'delay':
            translated = self._translate_with_dict(text, self.delay_dict)
        elif field_type == 'description':
            translated = self._translate_description(text)
        else:
            translated = text
        
        # 缓存结果
        self.cache[cache_key] = translated
        return translated
    
    def _translate_with_dict(self, text: str, translation_dict: Dict[str, str]) -> str:
        """使用字典翻译"""
        # 直接匹配
        if text in translation_dict:
            return translation_dict[text]
        
        # 部分匹配
        text_lower = text.lower()
        for en_text, cn_text in translation_dict.items():
            if en_text.lower() in text_lower:
                return text.replace(en_text, cn_text)
        
        return text
    
    def _translate_description(self, text: str) -> str:
        """翻译描述文本"""
        translated = text
        
        # 替换关键词
        for en_word, cn_word in self.description_keywords.items():
            # 使用正则表达式进行单词边界匹配
            pattern = r'\b' + re.escape(en_word) + r'\b'
            translated = re.sub(pattern, cn_word, translated, flags=re.IGNORECASE)
        
        # 处理数字+时间单位的模式
        translated = re.sub(r'(\d+)\s*year', r'\1年', translated, flags=re.IGNORECASE)
        translated = re.sub(r'(\d+)\s*month', r'\1个月', translated, flags=re.IGNORECASE)
        translated = re.sub(r'(\d+)\s*quarter', r'\1季度', translated, flags=re.IGNORECASE)
        translated = re.sub(r'(\d+)\s*week', r'\1周', translated, flags=re.IGNORECASE)
        translated = re.sub(r'(\d+)\s*day', r'\1天', translated, flags=re.IGNORECASE)
        
        return translated

if __name__ == "__main__":
    # 测试简化翻译器
    print("🧪 Testing Simple Translator")
    print("=" * 50)
    
    translator = SimpleTranslator()
    
    # 测试不同类型的翻译
    test_cases = [
        ("Broker Estimates", "dataset"),
        ("Fundamental", "category"),
        ("Analyst Estimates", "subcategory"),
        ("USA", "region"),
        ("Real Time", "delay"),
        ("12 month trailing earnings per share", "description"),
        ("Consensus estimate for next quarter revenue", "description")
    ]
    
    for text, field_type in test_cases:
        translated = translator.translate_to_chinese(text, field_type)
        print(f"{field_type:12} | {text:40} → {translated}")
    
    print("\n✅ Simple translator test completed!")
