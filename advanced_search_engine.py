#!/usr/bin/env python3
"""
基于深度学习的高性能中文搜索引擎
使用BGE模型 + ChromaDB实现毫秒级搜索响应
"""

import os
import time
import json
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import pandas as pd
import numpy as np
from loguru import logger
import chromadb
from chromadb.config import Settings
from FlagEmbedding import FlagModel
import jieba
import re
from concurrent.futures import ThreadPoolExecutor
import asyncio
from dataclasses import dataclass
from enum import Enum

class SearchMode(Enum):
    """搜索模式"""
    SEMANTIC = "semantic"      # 纯语义搜索
    KEYWORD = "keyword"        # 关键词搜索
    HYBRID = "hybrid"          # 混合搜索

@dataclass
class SearchResult:
    """搜索结果数据结构"""
    id: str
    description: str
    dataset_name: str
    category_name: str
    subcategory_name: str
    score: float
    search_mode: str
    metadata: Dict[str, Any]

class ChineseTextProcessor:
    """中文文本处理器"""
    
    def __init__(self):
        self.stop_words = self._load_stop_words()
        # 预编译正则表达式
        self.clean_pattern = re.compile(r'[^\w\s\u4e00-\u9fff\-_.,()%/]')
        self.space_pattern = re.compile(r'\s+')
        
    def _load_stop_words(self) -> set:
        """加载停用词"""
        stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还是', '为了', '还有', '可以', '这个', '那个'
        }
        return stop_words
    
    def clean_text(self, text: str) -> str:
        """清洗文本"""
        if not text or pd.isna(text):
            return ""
        
        text = str(text)
        # 移除特殊字符但保留中文、英文、数字和基本标点
        text = self.clean_pattern.sub(' ', text)
        # 标准化空格
        text = self.space_pattern.sub(' ', text)
        return text.strip()
    
    def segment_text(self, text: str, remove_stopwords: bool = True) -> List[str]:
        """中文分词"""
        if not text:
            return []
        
        # 使用jieba分词
        words = jieba.lcut(text)
        
        # 过滤条件
        filtered_words = []
        for word in words:
            word = word.strip()
            if (len(word) >= 2 and 
                not word.isdigit() and 
                word not in self.stop_words if remove_stopwords else True):
                filtered_words.append(word)
        
        return filtered_words
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        words = self.segment_text(text, remove_stopwords=True)
        
        # 简单的词频统计
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:top_k]]

class AdvancedSearchEngine:
    """基于深度学习的高性能搜索引擎"""
    
    def __init__(self, 
                 model_name: str = "BAAI/bge-large-zh-v1.5",
                 chroma_path: str = "./chroma_db",
                 collection_name: str = "financial_data"):
        """
        初始化搜索引擎
        
        Args:
            model_name: BGE模型名称
            chroma_path: ChromaDB存储路径
            collection_name: 集合名称
        """
        self.model_name = model_name
        self.chroma_path = Path(chroma_path)
        self.collection_name = collection_name
        
        # 初始化组件
        self.text_processor = ChineseTextProcessor()
        self.embedding_model = None
        self.chroma_client = None
        self.collection = None
        
        # 缓存
        self.query_cache = {}
        self.max_cache_size = 1000
        
        logger.info(f"Initializing AdvancedSearchEngine with model: {model_name}")
    
    def _load_embedding_model(self):
        """加载嵌入模型"""
        if self.embedding_model is None:
            logger.info(f"Loading embedding model: {self.model_name}")
            try:
                # 尝试加载BGE模型
                self.embedding_model = FlagModel(
                    self.model_name,
                    query_instruction_for_retrieval="为这个句子生成表示以用于检索相关文章：",
                    use_fp16=True  # 使用半精度加速
                )
                logger.success("BGE model loaded successfully!")
            except Exception as e:
                logger.warning(f"Failed to load BGE model: {e}")
                # 回退到sentence-transformers
                from sentence_transformers import SentenceTransformer
                self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                logger.info("Fallback to sentence-transformers model")
    
    def _init_chroma_db(self):
        """初始化ChromaDB"""
        if self.chroma_client is None:
            logger.info("Initializing ChromaDB...")
            
            # 创建存储目录
            self.chroma_path.mkdir(parents=True, exist_ok=True)
            
            # 初始化客户端
            self.chroma_client = chromadb.PersistentClient(
                path=str(self.chroma_path),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 获取或创建集合
            try:
                self.collection = self.chroma_client.get_collection(
                    name=self.collection_name
                )
                logger.info(f"Loaded existing collection: {self.collection_name}")
            except:
                self.collection = self.chroma_client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "Financial data search collection"}
                )
                logger.info(f"Created new collection: {self.collection_name}")
    
    def encode_text(self, texts: List[str]) -> np.ndarray:
        """文本向量化"""
        if self.embedding_model is None:
            self._load_embedding_model()
        
        if hasattr(self.embedding_model, 'encode'):
            # BGE模型
            if isinstance(texts, str):
                texts = [texts]
            embeddings = self.embedding_model.encode(texts)
        else:
            # sentence-transformers模型
            embeddings = self.embedding_model.encode(texts)
        
        return embeddings
    
    def build_index(self, df: pd.DataFrame, batch_size: int = 100):
        """构建搜索索引"""
        logger.info(f"Building search index for {len(df)} records...")
        
        # 初始化组件
        self._load_embedding_model()
        self._init_chroma_db()
        
        # 清空现有数据
        try:
            self.chroma_client.delete_collection(self.collection_name)
            self.collection = self.chroma_client.create_collection(
                name=self.collection_name,
                metadata={"description": "Financial data search collection"}
            )
        except:
            pass
        
        # 准备数据
        search_fields = ['id', 'description', 'dataset.name', 'category.name', 'subcategory.name']
        
        def create_searchable_text(row):
            texts = []
            for field in search_fields:
                if field in row and pd.notna(row[field]):
                    cleaned = self.text_processor.clean_text(row[field])
                    if cleaned:
                        texts.append(cleaned)
            return " | ".join(texts)
        
        # 创建搜索文本
        logger.info("Creating searchable text...")
        df['searchable_text'] = df.apply(create_searchable_text, axis=1)
        
        # 批量处理
        total_batches = (len(df) + batch_size - 1) // batch_size
        
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            batch_num = i // batch_size + 1
            
            logger.info(f"Processing batch {batch_num}/{total_batches}")
            
            # 准备批次数据
            texts = batch_df['searchable_text'].tolist()
            ids = [f"doc_{idx}" for idx in batch_df.index]
            
            # 生成向量
            embeddings = self.encode_text(texts)
            
            # 准备元数据
            metadatas = []
            for _, row in batch_df.iterrows():
                metadata = {
                    'id': str(row['id']),
                    'description': str(row.get('description', '')),
                    'dataset_name': str(row.get('dataset.name', '')),
                    'category_name': str(row.get('category.name', '')),
                    'subcategory_name': str(row.get('subcategory.name', '')),
                    'region': str(row.get('region', '')),
                    'universe': str(row.get('universe', '')),
                    'delay': str(row.get('delay', ''))
                }
                metadatas.append(metadata)
            
            # 添加到ChromaDB
            self.collection.add(
                embeddings=embeddings.tolist(),
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
        
        logger.success(f"Index built successfully! Total documents: {len(df)}")
    
    def semantic_search(self, query: str, top_k: int = 10, 
                       filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """语义搜索"""
        if self.collection is None:
            raise ValueError("Index not built. Please build index first.")
        
        # 生成查询向量
        query_embedding = self.encode_text([query])[0]
        
        # 执行搜索
        results = self.collection.query(
            query_embeddings=[query_embedding.tolist()],
            n_results=top_k,
            where=filters
        )
        
        # 转换结果
        search_results = []
        for i in range(len(results['ids'][0])):
            metadata = results['metadatas'][0][i]
            result = SearchResult(
                id=metadata['id'],
                description=metadata['description'],
                dataset_name=metadata['dataset_name'],
                category_name=metadata['category_name'],
                subcategory_name=metadata['subcategory_name'],
                score=1.0 - results['distances'][0][i],  # 转换为相似度分数
                search_mode=SearchMode.SEMANTIC.value,
                metadata=metadata
            )
            search_results.append(result)
        
        return search_results
    
    def keyword_search(self, query: str, top_k: int = 10,
                      filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """关键词搜索"""
        if self.collection is None:
            raise ValueError("Index not built. Please build index first.")
        
        # 处理查询
        keywords = self.text_processor.segment_text(query)
        query_text = " ".join(keywords)
        
        # 使用ChromaDB的文本搜索
        results = self.collection.query(
            query_texts=[query_text],
            n_results=top_k,
            where=filters
        )
        
        # 转换结果
        search_results = []
        for i in range(len(results['ids'][0])):
            metadata = results['metadatas'][0][i]
            result = SearchResult(
                id=metadata['id'],
                description=metadata['description'],
                dataset_name=metadata['dataset_name'],
                category_name=metadata['category_name'],
                subcategory_name=metadata['subcategory_name'],
                score=1.0 - results['distances'][0][i],
                search_mode=SearchMode.KEYWORD.value,
                metadata=metadata
            )
            search_results.append(result)
        
        return search_results
    
    def hybrid_search(self, query: str, top_k: int = 10,
                     semantic_weight: float = 0.7,
                     keyword_weight: float = 0.3,
                     filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """混合搜索"""
        # 并行执行语义搜索和关键词搜索
        with ThreadPoolExecutor(max_workers=2) as executor:
            semantic_future = executor.submit(
                self.semantic_search, query, top_k * 2, filters
            )
            keyword_future = executor.submit(
                self.keyword_search, query, top_k * 2, filters
            )
            
            semantic_results = semantic_future.result()
            keyword_results = keyword_future.result()
        
        # 合并结果
        combined_results = {}
        
        # 添加语义搜索结果
        for result in semantic_results:
            result.score *= semantic_weight
            combined_results[result.id] = result
        
        # 添加关键词搜索结果
        for result in keyword_results:
            if result.id in combined_results:
                # 合并分数
                combined_results[result.id].score += result.score * keyword_weight
                combined_results[result.id].search_mode = SearchMode.HYBRID.value
            else:
                result.score *= keyword_weight
                result.search_mode = SearchMode.HYBRID.value
                combined_results[result.id] = result
        
        # 排序并返回top_k结果
        final_results = sorted(
            combined_results.values(),
            key=lambda x: x.score,
            reverse=True
        )
        
        return final_results[:top_k]
    
    def search(self, query: str, 
               mode: SearchMode = SearchMode.HYBRID,
               top_k: int = 10,
               filters: Optional[Dict[str, Any]] = None) -> List[SearchResult]:
        """统一搜索接口"""
        # 检查缓存
        cache_key = hashlib.md5(
            f"{query}_{mode.value}_{top_k}_{filters}".encode()
        ).hexdigest()
        
        if cache_key in self.query_cache:
            logger.debug(f"Cache hit for query: {query}")
            return self.query_cache[cache_key]
        
        # 执行搜索
        start_time = time.time()
        
        if mode == SearchMode.SEMANTIC:
            results = self.semantic_search(query, top_k, filters)
        elif mode == SearchMode.KEYWORD:
            results = self.keyword_search(query, top_k, filters)
        else:  # HYBRID
            results = self.hybrid_search(query, top_k, filters=filters)
        
        search_time = time.time() - start_time
        logger.info(f"Search completed in {search_time:.3f}s, found {len(results)} results")
        
        # 更新缓存
        if len(self.query_cache) >= self.max_cache_size:
            # 简单的LRU：删除最旧的条目
            oldest_key = next(iter(self.query_cache))
            del self.query_cache[oldest_key]
        
        self.query_cache[cache_key] = results
        
        return results
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if self.collection is None:
            return {"status": "not_initialized"}
        
        count = self.collection.count()
        
        return {
            "status": "ready",
            "total_documents": count,
            "model_name": self.model_name,
            "collection_name": self.collection_name,
            "cache_size": len(self.query_cache)
        }

if __name__ == "__main__":
    # 示例用法
    engine = AdvancedSearchEngine()
    
    # 加载数据
    print("Loading data...")
    df = pd.read_csv("data.csv")
    
    # 选择单一region_universe_delay组合进行测试
    test_df = df[(df['region'] == 'USA') & 
                 (df['universe'] == 'TOP3000') & 
                 (df['delay'] == 1)].head(1000)  # 测试用1000条数据
    
    print(f"Building index for {len(test_df)} records...")
    engine.build_index(test_df)
    
    # 测试搜索
    test_queries = [
        "earnings per share",
        "每股收益",
        "EBITDA",
        "分析师估算"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = engine.search(query, mode=SearchMode.HYBRID, top_k=5)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.id} (Score: {result.score:.4f})")
            print(f"   {result.description[:100]}...")
            print(f"   Dataset: {result.dataset_name}")
