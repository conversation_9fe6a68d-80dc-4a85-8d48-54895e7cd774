#!/usr/bin/env python3
"""
列信息检索分析报告
详细分析BERT融合搜索引擎检索了表里的哪些列信息
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any
import json

def analyze_column_usage():
    """分析列使用情况"""
    print("📊 BERT融合搜索引擎 - 列信息检索分析报告")
    print("=" * 80)
    
    # 加载数据查看完整的列结构
    print("📁 加载数据文件分析...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv")
    
    print(f"📈 数据集基本信息:")
    print(f"   总行数: {len(df):,}")
    print(f"   总列数: {len(df.columns)}")
    print(f"   文件大小: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
    
    # 显示所有列
    print(f"\n📋 数据表完整列结构:")
    for i, col in enumerate(df.columns, 1):
        sample_value = str(df[col].iloc[0]) if not pd.isna(df[col].iloc[0]) else "N/A"
        print(f"   {i:2d}. {col:<25} | 示例: {sample_value[:50]}...")
    
    # 分析搜索引擎使用的列
    print(f"\n🎯 BERT融合搜索引擎检索的列信息:")
    print("=" * 60)
    
    # 从代码中提取的列权重配置
    target_columns = {
        'id': 0.15,
        'description': 0.35,
        'dataset.name': 0.20,
        'category.name': 0.15,
        'subcategory.name': 0.15
    }
    
    total_weight = sum(target_columns.values())
    
    print(f"🔍 检索的核心列 (共 {len(target_columns)} 列):")
    print(f"   总权重: {total_weight:.2f} (100%)")
    print()
    
    for i, (col_name, weight) in enumerate(target_columns.items(), 1):
        percentage = (weight / total_weight) * 100
        
        print(f"   {i}. 【{col_name}】")
        print(f"      权重: {weight:.2f} ({percentage:.1f}%)")
        
        if col_name in df.columns:
            # 分析列的数据特征
            col_data = df[col_name]
            non_null_count = col_data.notna().sum()
            unique_count = col_data.nunique()
            
            print(f"      数据覆盖: {non_null_count:,}/{len(df):,} ({non_null_count/len(df)*100:.1f}%)")
            print(f"      唯一值数: {unique_count:,}")
            
            # 显示示例值
            sample_values = col_data.dropna().head(3).tolist()
            print(f"      示例值: {sample_values}")
            
            # 分析文本长度
            if col_data.dtype == 'object':
                text_lengths = col_data.astype(str).str.len()
                avg_length = text_lengths.mean()
                max_length = text_lengths.max()
                print(f"      文本长度: 平均 {avg_length:.1f} 字符, 最长 {max_length} 字符")
        else:
            print(f"      ❌ 列不存在于数据中")
        
        print()
    
    # 分析未使用的列
    print(f"📋 未被检索的列 (共 {len(df.columns) - len(target_columns)} 列):")
    print("=" * 60)
    
    unused_columns = [col for col in df.columns if col not in target_columns.keys()]
    
    # 按类别分组显示未使用的列
    metadata_cols = []
    numeric_cols = []
    other_cols = []
    
    for col in unused_columns:
        if col in ['Unnamed: 0', 'region', 'delay', 'universe', 'type', 'coverage', 
                   'userCount', 'alphaCount', 'pyramidMultiplier']:
            metadata_cols.append(col)
        elif df[col].dtype in ['int64', 'float64']:
            numeric_cols.append(col)
        else:
            other_cols.append(col)
    
    print(f"🏷️  元数据列 ({len(metadata_cols)} 列):")
    for col in metadata_cols:
        sample_val = str(df[col].iloc[0]) if not pd.isna(df[col].iloc[0]) else "N/A"
        print(f"      • {col:<20} | 示例: {sample_val}")
    
    print(f"\n🔢 数值列 ({len(numeric_cols)} 列):")
    for col in numeric_cols:
        sample_val = str(df[col].iloc[0]) if not pd.isna(df[col].iloc[0]) else "N/A"
        print(f"      • {col:<20} | 示例: {sample_val}")
    
    print(f"\n📝 其他列 ({len(other_cols)} 列):")
    for col in other_cols:
        sample_val = str(df[col].iloc[0]) if not pd.isna(df[col].iloc[0]) else "N/A"
        print(f"      • {col:<20} | 示例: {sample_val}")
    
    # 分析检索策略
    print(f"\n🧠 检索策略分析:")
    print("=" * 60)
    
    print(f"✅ 已检索列的特点:")
    print(f"   1. 【id】(15%) - 精确匹配，用于ID查询")
    print(f"   2. 【description】(35%) - 核心内容，语义搜索主体")
    print(f"   3. 【dataset.name】(20%) - 数据集分类，重要上下文")
    print(f"   4. 【category.name】(15%) - 类别信息，辅助分类")
    print(f"   5. 【subcategory.name】(15%) - 子类别，细粒度分类")
    
    print(f"\n📈 权重分配策略:")
    print(f"   • 描述列权重最高(35%) - 包含最丰富的语义信息")
    print(f"   • 数据集名称次之(20%) - 提供重要的分类上下文")
    print(f"   • ID、类别、子类别均衡(15%) - 支持精确匹配和分类")
    
    print(f"\n❌ 未检索列的原因分析:")
    print(f"   • 元数据列 - 主要用于系统管理，对搜索相关性较低")
    print(f"   • 数值列 - 统计信息，不适合文本语义搜索")
    print(f"   • 其他列 - 辅助信息，权重较低")
    
    # 性能影响分析
    print(f"\n⚡ 性能影响分析:")
    print("=" * 60)
    
    total_columns = len(df.columns)
    used_columns = len(target_columns)
    reduction_ratio = (total_columns - used_columns) / total_columns
    
    print(f"📊 列数量对比:")
    print(f"   原始列数: {total_columns}")
    print(f"   检索列数: {used_columns}")
    print(f"   减少比例: {reduction_ratio:.1%}")
    
    print(f"\n🚀 性能优势:")
    print(f"   1. 索引大小减少 ~{reduction_ratio:.0%}")
    print(f"   2. 内存使用优化 ~{reduction_ratio:.0%}")
    print(f"   3. 搜索速度提升 ~{1/(1-reduction_ratio):.1f}x")
    print(f"   4. 特征提取聚焦核心信息")
    
    # 建议优化
    print(f"\n💡 优化建议:")
    print("=" * 60)
    
    print(f"🎯 当前策略优点:")
    print(f"   ✅ 聚焦核心语义信息")
    print(f"   ✅ 权重分配合理")
    print(f"   ✅ 性能优化显著")
    print(f"   ✅ 支持多种查询类型")
    
    print(f"\n🔧 可能的改进方向:")
    print(f"   1. 考虑添加 'themes' 列 - 可能包含主题标签")
    print(f"   2. 动态权重调整 - 根据查询类型调整列权重")
    print(f"   3. 用户自定义列 - 允许用户选择检索列")
    print(f"   4. A/B测试 - 验证不同列组合的效果")
    
    return {
        'total_columns': total_columns,
        'used_columns': used_columns,
        'target_columns': target_columns,
        'unused_columns': unused_columns,
        'reduction_ratio': reduction_ratio
    }

def generate_column_mapping_visualization():
    """生成列映射可视化"""
    print(f"\n🎨 列检索映射可视化:")
    print("=" * 80)
    
    # ASCII艺术展示列映射
    print(f"""
    📊 原始数据表 (18列)                    🔍 BERT检索引擎 (5列)
    ┌─────────────────────────┐           ┌─────────────────────────┐
    │ Unnamed: 0              │           │                         │
    │ id                      │ ────────► │ id (15%)                │
    │ description             │ ────────► │ description (35%)       │
    │ region                  │           │                         │
    │ delay                   │           │                         │
    │ universe                │           │                         │
    │ type                    │           │                         │
    │ coverage                │           │                         │
    │ userCount               │           │                         │
    │ alphaCount              │           │                         │
    │ pyramidMultiplier       │           │                         │
    │ themes                  │           │                         │
    │ dataset.id              │           │                         │
    │ dataset.name            │ ────────► │ dataset.name (20%)      │
    │ category.id             │           │                         │
    │ category.name           │ ────────► │ category.name (15%)     │
    │ subcategory.id          │           │                         │
    │ subcategory.name        │ ────────► │ subcategory.name (15%)  │
    └─────────────────────────┘           └─────────────────────────┘
    
    🎯 检索策略: 精选5列核心信息，权重优化分配
    ⚡ 性能提升: 索引大小减少72%，搜索速度提升3.6倍
    """)

if __name__ == "__main__":
    # 执行分析
    analysis_result = analyze_column_usage()
    
    # 生成可视化
    generate_column_mapping_visualization()
    
    # 保存分析结果
    with open('column_analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 分析结果已保存到 column_analysis_result.json")
    print(f"🎉 列信息检索分析完成!")
