#!/usr/bin/env python3
"""
快速交互式搜索演示
使用较少数据快速展示交互式阈值调整功能
"""

import time
import pandas as pd
import numpy as np
from interactive_search_engine import InteractiveSearchEngine

def quick_demo():
    """快速演示"""
    print("🚀 Quick Interactive Search Demo")
    print("=" * 60)
    
    # 初始化搜索引擎
    search_engine = InteractiveSearchEngine()
    
    # 加载少量测试数据进行快速演示
    print("📊 Loading sample data (500 records for quick demo)...")
    df = pd.read_csv("split_files/USA_1_TOP3000.csv").head(500)
    search_engine.engine.build_fusion_index(df)
    
    # 创建演示会话
    session = search_engine.create_user_session("quick_demo")
    
    print("\n🎉 Quick Demo Ready!")
    print("\n📖 Interactive Commands:")
    print("   🔍 Search: Just type your query")
    print("   📉 More results: '更多结果' or 'more'")
    print("   📈 More precise: '更精准' or 'precise'")
    print("   🔄 Reset: '重置' or 'reset'")
    
    # 演示搜索场景
    demo_scenarios = [
        {
            'description': "初始搜索 - 每股收益",
            'query': "每股收益",
            'expected': "可能结果较少，因为使用默认阈值"
        },
        {
            'description': "降低阈值 - 获得更多结果",
            'query': "更多结果",
            'expected': "重新搜索，结果数量应该增加"
        },
        {
            'description': "提高阈值 - 获得更精准结果",
            'query': "更精准",
            'expected': "重新搜索，结果更精准但可能数量减少"
        },
        {
            'description': "新搜索 - 分析师预测",
            'query': "分析师预测",
            'expected': "使用当前用户偏好的阈值"
        },
        {
            'description': "再次调整 - 更多结果",
            'query': "更多",
            'expected': "进一步降低阈值"
        }
    ]
    
    print("\n" + "="*60)
    print("🎬 自动演示交互式阈值调整")
    print("="*60)
    
    for i, scenario in enumerate(demo_scenarios, 1):
        print(f"\n📋 场景 {i}: {scenario['description']}")
        print(f"💭 预期: {scenario['expected']}")
        print(f"🔍 执行: '{scenario['query']}'")
        
        # 模拟用户输入
        input(f"按回车键继续...")
        
        start_time = time.time()
        results = search_engine.handle_interaction(scenario['query'], "quick_demo")
        search_time = time.time() - start_time
        
        # 显示结果
        if results:
            print(f"\n✅ 找到 {len(results)} 个结果 (用时 {search_time:.3f}s):")
            print("-" * 40)
            
            for j, result in enumerate(results[:3], 1):  # 显示前3个结果
                print(f"{j}. {result.id} (分数: {result.overall_score:.3f})")
                print(f"   描述: {result.description[:60]}...")
                print()
            
            if len(results) > 3:
                print(f"... 还有 {len(results) - 3} 个结果")
        else:
            print("❌ 未找到匹配结果")
        
        # 显示当前阈值状态
        stats = search_engine.get_session_stats("quick_demo")
        print(f"\n📊 当前状态:")
        print(f"   阈值 - 最小: {stats['current_thresholds']['min_score']:.3f}, "
              f"语义: {stats['current_thresholds']['semantic']:.3f}, "
              f"关键词: {stats['current_thresholds']['keyword']:.3f}")
        print(f"   用户偏好: {stats['preference_score']:.2f} (0=更多结果, 1=更精准)")
        print(f"   阈值趋势: {stats['threshold_trend']}")
    
    # 最终统计
    print(f"\n📈 演示总结:")
    final_stats = search_engine.get_session_stats("quick_demo")
    print(f"   总搜索次数: {final_stats['total_searches']}")
    print(f"   平均结果数: {final_stats['avg_results_per_search']:.1f}")
    print(f"   平均分数: {final_stats['avg_score']:.3f}")
    print(f"   最终偏好: {final_stats['preference_score']:.2f}")
    
    print(f"\n🎉 演示完成!")
    print(f"💡 核心特性:")
    print(f"   ✅ 智能阈值调整 - 根据用户指令动态调整")
    print(f"   ✅ 记忆用户偏好 - 保持个性化设置")
    print(f"   ✅ 实时反馈 - 立即看到调整效果")
    print(f"   ✅ 简单交互 - 中英文指令都支持")
    
    # 询问是否进入交互模式
    print(f"\n🤔 想要尝试手动交互吗? (y/n)")
    choice = input().lower().strip()
    
    if choice in ['y', 'yes', '是', '好']:
        print(f"\n🎮 进入交互模式...")
        print(f"输入搜索查询或阈值调整指令 (输入 'quit' 退出):")
        
        while True:
            try:
                user_input = input("\n🔍 > ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                
                start_time = time.time()
                results = search_engine.handle_interaction(user_input, "quick_demo")
                search_time = time.time() - start_time
                
                if results:
                    print(f"\n✅ 找到 {len(results)} 个结果 (用时 {search_time:.3f}s):")
                    for j, result in enumerate(results[:3], 1):
                        print(f"{j}. {result.id} (分数: {result.overall_score:.3f})")
                        print(f"   {result.description[:70]}...")
                    
                    if len(results) > 3:
                        print(f"... 还有 {len(results) - 3} 个结果")
                else:
                    print("❌ 未找到匹配结果")
                    print("💡 尝试输入'更多结果'降低阈值")
                
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

if __name__ == "__main__":
    quick_demo()
