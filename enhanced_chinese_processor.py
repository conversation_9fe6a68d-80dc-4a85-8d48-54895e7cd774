#!/usr/bin/env python3
"""
增强的中文文本处理模块
集成多种中文NLP工具，提供最佳的中文文本处理效果
"""

import re
import jieba
import jieba.posseg as pseg
from typing import List, Dict, Set, Tuple, Optional
import pandas as pd
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

class EntityType(Enum):
    """实体类型"""
    FINANCIAL_METRIC = "financial_metric"    # 财务指标
    COMPANY = "company"                      # 公司名称
    INDUSTRY = "industry"                    # 行业
    TIME_PERIOD = "time_period"             # 时间周期
    CURRENCY = "currency"                    # 货币
    PERCENTAGE = "percentage"               # 百分比

@dataclass
class Entity:
    """命名实体"""
    text: str
    entity_type: EntityType
    start: int
    end: int
    confidence: float = 1.0

@dataclass
class ProcessedText:
    """处理后的文本"""
    original: str
    cleaned: str
    tokens: List[str]
    keywords: List[str]
    entities: List[Entity]
    pos_tags: List[Tuple[str, str]]

class EnhancedChineseProcessor:
    """增强的中文文本处理器"""
    
    def __init__(self):
        self.financial_terms = self._load_financial_terms()
        self.stop_words = self._load_stop_words()
        self.synonym_dict = self._load_synonyms()
        
        # 预编译正则表达式
        self.patterns = {
            'clean': re.compile(r'[^\w\s\u4e00-\u9fff\-_.,()%/]'),
            'space': re.compile(r'\s+'),
            'percentage': re.compile(r'\d+\.?\d*%'),
            'currency': re.compile(r'[¥$€£]\d+\.?\d*[万亿千百十]?'),
            'number': re.compile(r'\d+\.?\d*'),
            'time_period': re.compile(r'\d+[年月日季度]|[上下]半年|年初|年末|季末|月末'),
            'financial_ratio': re.compile(r'(每股|净资产|总资产|营业|利润|收入|成本).{0,10}(率|比|额|值)')
        }
        
        # 初始化jieba
        self._init_jieba()
    
    def _load_financial_terms(self) -> Set[str]:
        """加载金融术语词典"""
        terms = {
            # 基础财务指标
            '每股收益', 'EPS', '净资产收益率', 'ROE', '总资产收益率', 'ROA',
            '毛利率', '净利率', '营业利润率', '资产负债率', '流动比率',
            '速动比率', '存货周转率', '应收账款周转率', '总资产周转率',
            
            # 估值指标
            '市盈率', 'PE', '市净率', 'PB', '市销率', 'PS', '企业价值倍数', 'EV',
            '价格现金流比率', 'PCF', '股息收益率', '股价净值比',
            
            # 现金流指标
            '经营现金流', '自由现金流', '现金流量', '现金及现金等价物',
            '投资现金流', '筹资现金流', '现金流量净额',
            
            # 盈利指标
            '营业收入', '营业利润', '净利润', '毛利润', '息税前利润', 'EBIT',
            '息税折旧摊销前利润', 'EBITDA', '归母净利润', '扣非净利润',
            
            # 资产负债指标
            '总资产', '净资产', '负债总额', '股东权益', '流动资产',
            '非流动资产', '流动负债', '非流动负债', '货币资金',
            
            # 增长指标
            '营收增长率', '净利润增长率', '资产增长率', '同比增长',
            '环比增长', '复合增长率', 'CAGR',
            
            # 分析师相关
            '分析师', '研报', '评级', '目标价', '盈利预测', '一致预期',
            '买入', '卖出', '持有', '增持', '减持', '中性'
        }
        
        # 添加英文缩写
        english_terms = {
            'P/E', 'P/B', 'P/S', 'D/E', 'ROI', 'ROIC', 'WACC',
            'FCF', 'OCF', 'CAPEX', 'OPEX', 'SG&A', 'R&D',
            'YoY', 'QoQ', 'MoM', 'TTM', 'LTM'
        }
        
        return terms.union(english_terms)
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词"""
        stop_words = {
            # 基础停用词
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有',
            '看', '好', '自己', '这', '那', '里', '就是', '还是', '为了', '还有',
            '可以', '这个', '那个', '什么', '怎么', '为什么', '因为', '所以',
            
            # 金融领域停用词
            '数据', '指标', '公司', '企业', '股票', '证券', '市场', '行业',
            '报告', '分析', '预测', '估计', '大约', '左右', '以上', '以下',
            '相关', '主要', '重要', '关键', '核心', '基本', '一般', '通常'
        }
        return stop_words
    
    def _load_synonyms(self) -> Dict[str, List[str]]:
        """加载同义词词典"""
        synonyms = {
            '每股收益': ['EPS', '每股盈利', '每股净收益'],
            '净资产收益率': ['ROE', '股东权益回报率', '净资产报酬率'],
            '总资产收益率': ['ROA', '资产回报率', '总资产报酬率'],
            '市盈率': ['PE', 'P/E', '价格收益比'],
            '市净率': ['PB', 'P/B', '价格净值比'],
            '营业收入': ['营收', '销售收入', '主营业务收入', '收入'],
            '净利润': ['净收益', '税后利润', '净盈利'],
            '毛利率': ['毛利润率', '销售毛利率'],
            '分析师': ['研究员', '分析员', '证券分析师'],
            '增长率': ['增长幅度', '增速', '涨幅']
        }
        return synonyms
    
    def _init_jieba(self):
        """初始化jieba分词器"""
        # 添加金融术语到jieba词典
        for term in self.financial_terms:
            jieba.add_word(term, freq=1000, tag='financial')
        
        # 添加同义词
        for key, synonyms in self.synonym_dict.items():
            for synonym in synonyms:
                jieba.add_word(synonym, freq=800, tag='financial')
    
    def clean_text(self, text: str) -> str:
        """清洗文本"""
        if not text or pd.isna(text):
            return ""
        
        text = str(text)
        # 保留更多有用字符
        text = self.patterns['clean'].sub(' ', text)
        text = self.patterns['space'].sub(' ', text)
        return text.strip()
    
    def extract_entities(self, text: str) -> List[Entity]:
        """提取命名实体"""
        entities = []
        
        # 提取百分比
        for match in self.patterns['percentage'].finditer(text):
            entities.append(Entity(
                text=match.group(),
                entity_type=EntityType.PERCENTAGE,
                start=match.start(),
                end=match.end()
            ))
        
        # 提取货币
        for match in self.patterns['currency'].finditer(text):
            entities.append(Entity(
                text=match.group(),
                entity_type=EntityType.CURRENCY,
                start=match.start(),
                end=match.end()
            ))
        
        # 提取时间周期
        for match in self.patterns['time_period'].finditer(text):
            entities.append(Entity(
                text=match.group(),
                entity_type=EntityType.TIME_PERIOD,
                start=match.start(),
                end=match.end()
            ))
        
        # 提取财务指标
        words = jieba.lcut(text)
        pos = 0
        for word in words:
            if word in self.financial_terms:
                start = text.find(word, pos)
                if start != -1:
                    entities.append(Entity(
                        text=word,
                        entity_type=EntityType.FINANCIAL_METRIC,
                        start=start,
                        end=start + len(word),
                        confidence=0.9
                    ))
                    pos = start + len(word)
        
        return entities
    
    def segment_with_pos(self, text: str) -> List[Tuple[str, str]]:
        """分词并标注词性"""
        if not text:
            return []
        
        # 使用jieba进行词性标注
        words_with_pos = pseg.lcut(text)
        
        # 过滤和处理
        filtered_results = []
        for word, pos in words_with_pos:
            word = word.strip()
            if (len(word) >= 1 and 
                not word.isspace() and
                word not in self.stop_words):
                filtered_results.append((word, pos))
        
        return filtered_results
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        # 分词并获取词性
        words_with_pos = self.segment_with_pos(text)
        
        # 计算词权重
        word_weights = {}
        for word, pos in words_with_pos:
            # 根据词性和是否为金融术语调整权重
            weight = 1.0
            
            if word in self.financial_terms:
                weight *= 3.0  # 金融术语权重高
            
            if pos in ['n', 'nr', 'ns', 'nt', 'nz']:  # 名词类
                weight *= 2.0
            elif pos in ['v', 'vn']:  # 动词类
                weight *= 1.5
            elif pos in ['a', 'ad']:  # 形容词类
                weight *= 1.2
            
            # 长词权重更高
            if len(word) >= 3:
                weight *= 1.5
            elif len(word) >= 2:
                weight *= 1.2
            
            word_weights[word] = word_weights.get(word, 0) + weight
        
        # 排序并返回top_k
        sorted_words = sorted(word_weights.items(), key=lambda x: x[1], reverse=True)
        return [word for word, weight in sorted_words[:top_k]]
    
    def expand_query(self, query: str) -> List[str]:
        """查询扩展"""
        expanded_terms = [query]
        
        # 分词
        words = jieba.lcut(query)
        
        # 添加同义词
        for word in words:
            if word in self.synonym_dict:
                expanded_terms.extend(self.synonym_dict[word])
            
            # 查找包含该词的同义词组
            for key, synonyms in self.synonym_dict.items():
                if word in synonyms:
                    expanded_terms.append(key)
                    expanded_terms.extend(synonyms)
        
        # 去重并返回
        return list(set(expanded_terms))
    
    def process_text(self, text: str) -> ProcessedText:
        """完整的文本处理"""
        if not text:
            return ProcessedText("", "", [], [], [], [])
        
        # 清洗文本
        cleaned = self.clean_text(text)
        
        # 分词和词性标注
        pos_tags = self.segment_with_pos(cleaned)
        tokens = [word for word, pos in pos_tags]
        
        # 提取关键词
        keywords = self.extract_keywords(cleaned)
        
        # 提取实体
        entities = self.extract_entities(cleaned)
        
        return ProcessedText(
            original=text,
            cleaned=cleaned,
            tokens=tokens,
            keywords=keywords,
            entities=entities,
            pos_tags=pos_tags
        )
    
    def analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """分析查询意图"""
        processed = self.process_text(query)
        
        # 分析查询类型
        query_type = "general"
        if any(entity.entity_type == EntityType.FINANCIAL_METRIC for entity in processed.entities):
            query_type = "financial_metric"
        elif any(word in self.financial_terms for word in processed.tokens):
            query_type = "financial_related"
        
        # 提取关键信息
        financial_terms = [word for word in processed.tokens if word in self.financial_terms]
        time_entities = [e.text for e in processed.entities if e.entity_type == EntityType.TIME_PERIOD]
        
        return {
            "query_type": query_type,
            "financial_terms": financial_terms,
            "time_entities": time_entities,
            "keywords": processed.keywords,
            "entities": [{"text": e.text, "type": e.entity_type.value} for e in processed.entities],
            "expanded_query": self.expand_query(query)
        }

if __name__ == "__main__":
    # 测试代码
    processor = EnhancedChineseProcessor()
    
    test_queries = [
        "每股收益相关指标",
        "2023年净资产收益率",
        "分析师对EBITDA的预测",
        "营业收入增长率超过10%的公司",
        "市盈率低于15倍的股票"
    ]
    
    for query in test_queries:
        print(f"\n🔍 查询: '{query}'")
        
        # 文本处理
        processed = processor.process_text(query)
        print(f"   分词: {processed.tokens}")
        print(f"   关键词: {processed.keywords}")
        
        # 意图分析
        intent = processor.analyze_query_intent(query)
        print(f"   查询类型: {intent['query_type']}")
        print(f"   金融术语: {intent['financial_terms']}")
        print(f"   扩展查询: {intent['expanded_query'][:3]}...")  # 只显示前3个
