#!/usr/bin/env python3
"""
传统文本搜索引擎 - 基于关键词、模糊匹配的传统搜索方法
"""

import pandas as pd
import numpy as np
import jieba
import re
from typing import List, Dict, Any, Tuple, Optional
from fuzzywuzzy import fuzz, process
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import os

class TraditionalSearchEngine:
    """传统搜索引擎"""
    
    def __init__(self):
        """初始化传统搜索引擎"""
        self.df = None
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        self.search_fields = ['id', 'description', 'dataset.id', 'dataset.name', 
                             'category.id', 'category.name', 'subcategory.id', 'subcategory.name']
    
    def load_processed_data(self, data_dir: str = "processed_data"):
        """
        加载预处理后的数据
        
        Args:
            data_dir: 数据目录
        """
        print("Loading processed data for traditional search...")
        
        # 加载DataFrame
        df_path = os.path.join(data_dir, "processed_data.pkl")
        self.df = pd.read_pickle(df_path)
        
        print(f"Loaded {len(self.df)} records")
        
        # 构建TF-IDF索引
        self.build_tfidf_index()
    
    def build_tfidf_index(self):
        """构建TF-IDF索引"""
        print("Building TF-IDF index...")
        
        # 使用关键词字段构建TF-IDF
        documents = self.df['keywords'].fillna('').tolist()
        
        # 创建TF-IDF向量化器
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 2),  # 使用1-gram和2-gram
            min_df=2,  # 至少出现在2个文档中
            max_df=0.8,  # 最多出现在80%的文档中
            stop_words=None  # 不使用停用词，因为我们的数据比较专业
        )
        
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(documents)
        print(f"TF-IDF index built with {self.tfidf_matrix.shape[1]} features")
    
    def preprocess_query(self, query: str) -> str:
        """
        预处理查询文本
        
        Args:
            query: 原始查询
            
        Returns:
            预处理后的查询
        """
        # 清洗文本
        query = re.sub(r'[^\w\s\u4e00-\u9fff\-_.,()%/]', ' ', query)
        query = re.sub(r'\s+', ' ', query).strip()
        
        # 中文分词
        words = jieba.lcut(query)
        filtered_words = [word for word in words 
                         if len(word) >= 2 and not word.isdigit() and word.strip()]
        
        return ' '.join(filtered_words)
    
    def exact_match_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        精确匹配搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        results = []
        query_lower = query.lower()
        
        for idx, row in self.df.iterrows():
            score = 0
            matched_fields = []
            
            # 检查每个搜索字段
            for field in self.search_fields:
                if field in row and pd.notna(row[field]):
                    field_value = str(row[field]).lower()
                    if query_lower in field_value:
                        # 根据匹配程度给分
                        if query_lower == field_value:
                            score += 100  # 完全匹配
                        elif field_value.startswith(query_lower):
                            score += 80   # 前缀匹配
                        elif field_value.endswith(query_lower):
                            score += 70   # 后缀匹配
                        else:
                            score += 50   # 包含匹配
                        
                        matched_fields.append(field)
            
            if score > 0:
                result = {
                    'rank': 0,  # 稍后排序时设置
                    'score': score,
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_id': row.get('dataset.id', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_id': row.get('category.id', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_id': row.get('subcategory.id', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'searchable_text': row['searchable_text'],
                    'matched_fields': matched_fields,
                    'search_type': 'exact'
                }
                results.append(result)
        
        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        # 设置排名
        for i, result in enumerate(results[:top_k]):
            result['rank'] = i + 1
        
        return results[:top_k]
    
    def fuzzy_search(self, query: str, top_k: int = 10, 
                    threshold: int = 60) -> List[Dict[str, Any]]:
        """
        模糊搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            threshold: 相似度阈值
            
        Returns:
            搜索结果列表
        """
        results = []
        
        for idx, row in self.df.iterrows():
            max_score = 0
            best_match_field = None
            
            # 检查每个搜索字段的模糊匹配
            for field in self.search_fields:
                if field in row and pd.notna(row[field]):
                    field_value = str(row[field])
                    
                    # 使用多种模糊匹配算法
                    ratio_score = fuzz.ratio(query, field_value)
                    partial_score = fuzz.partial_ratio(query, field_value)
                    token_sort_score = fuzz.token_sort_ratio(query, field_value)
                    token_set_score = fuzz.token_set_ratio(query, field_value)
                    
                    # 取最高分
                    field_score = max(ratio_score, partial_score, 
                                    token_sort_score, token_set_score)
                    
                    if field_score > max_score:
                        max_score = field_score
                        best_match_field = field
            
            if max_score >= threshold:
                result = {
                    'rank': 0,
                    'score': max_score,
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_id': row.get('dataset.id', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_id': row.get('category.id', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_id': row.get('subcategory.id', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'searchable_text': row['searchable_text'],
                    'best_match_field': best_match_field,
                    'search_type': 'fuzzy'
                }
                results.append(result)
        
        # 按分数排序
        results.sort(key=lambda x: x['score'], reverse=True)
        
        # 设置排名
        for i, result in enumerate(results[:top_k]):
            result['rank'] = i + 1
        
        return results[:top_k]
    
    def tfidf_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        TF-IDF搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        if self.tfidf_vectorizer is None or self.tfidf_matrix is None:
            return []
        
        # 预处理查询
        processed_query = self.preprocess_query(query)
        
        # 向量化查询
        query_vector = self.tfidf_vectorizer.transform([processed_query])
        
        # 计算相似度
        similarities = cosine_similarity(query_vector, self.tfidf_matrix).flatten()
        
        # 获取top-k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for i, idx in enumerate(top_indices):
            if similarities[idx] > 0:  # 只返回有相似度的结果
                row = self.df.iloc[idx]
                result = {
                    'rank': i + 1,
                    'score': float(similarities[idx]),
                    'id': row['id'],
                    'description': row['description'],
                    'dataset_id': row.get('dataset.id', ''),
                    'dataset_name': row.get('dataset.name', ''),
                    'category_id': row.get('category.id', ''),
                    'category_name': row.get('category.name', ''),
                    'subcategory_id': row.get('subcategory.id', ''),
                    'subcategory_name': row.get('subcategory.name', ''),
                    'searchable_text': row['searchable_text'],
                    'search_type': 'tfidf'
                }
                results.append(result)
        
        return results
    
    def field_specific_search(self, query: str, field: str, 
                            top_k: int = 10) -> List[Dict[str, Any]]:
        """
        特定字段搜索
        
        Args:
            query: 查询文本
            field: 搜索字段
            top_k: 返回结果数量
            
        Returns:
            搜索结果列表
        """
        if field not in self.search_fields or field not in self.df.columns:
            return []
        
        results = []
        query_lower = query.lower()
        
        # 过滤包含查询的记录
        mask = self.df[field].astype(str).str.lower().str.contains(query_lower, na=False)
        filtered_df = self.df[mask]
        
        for idx, row in filtered_df.iterrows():
            field_value = str(row[field]).lower()
            
            # 计算匹配分数
            if query_lower == field_value:
                score = 100
            elif field_value.startswith(query_lower):
                score = 90
            elif field_value.endswith(query_lower):
                score = 80
            else:
                score = 70
            
            result = {
                'rank': 0,
                'score': score,
                'id': row['id'],
                'description': row['description'],
                'dataset_id': row.get('dataset.id', ''),
                'dataset_name': row.get('dataset.name', ''),
                'category_id': row.get('category.id', ''),
                'category_name': row.get('category.name', ''),
                'subcategory_id': row.get('subcategory.id', ''),
                'subcategory_name': row.get('subcategory.name', ''),
                'searchable_text': row['searchable_text'],
                'matched_field': field,
                'search_type': f'field_{field}'
            }
            results.append(result)
        
        # 排序并设置排名
        results.sort(key=lambda x: x['score'], reverse=True)
        for i, result in enumerate(results[:top_k]):
            result['rank'] = i + 1
        
        return results[:top_k]

if __name__ == "__main__":
    # 示例用法
    engine = TraditionalSearchEngine()
    
    # 加载数据
    engine.load_processed_data()
    
    # 测试不同类型的搜索
    query = "earnings per share"
    
    print("Exact match results:")
    exact_results = engine.exact_match_search(query, top_k=3)
    for result in exact_results:
        print(f"Rank {result['rank']}: {result['id']} - Score: {result['score']}")
    
    print("\nFuzzy search results:")
    fuzzy_results = engine.fuzzy_search(query, top_k=3)
    for result in fuzzy_results:
        print(f"Rank {result['rank']}: {result['id']} - Score: {result['score']}")
    
    print("\nTF-IDF search results:")
    tfidf_results = engine.tfidf_search(query, top_k=3)
    for result in tfidf_results:
        print(f"Rank {result['rank']}: {result['id']} - Score: {result['score']:.4f}")
